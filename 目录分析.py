# import os
#
#
# def print_directory_tree(root_dir, prefix=''):
#     """
#     递归打印目录树状图
#     """
#     # 获取当前目录下的所有文件和文件夹
#     items = os.listdir(root_dir)
#
#     # 过滤掉隐藏文件（以.开头）
#     items = [item for item in items if not item.startswith('.')]
#     items.sort()
#
#     for i, item in enumerate(items):
#         item_path = os.path.join(root_dir, item)
#         is_last = i == len(items) - 1
#
#         # 当前项的连接线
#         connector = '└── ' if is_last else '├── '
#         print(prefix + connector + item)
#
#         # 如果是目录，递归打印
#         if os.path.isdir(item_path):
#             extension = '    ' if is_last else '│   '
#             print_directory_tree(item_path, prefix + extension)
#
#
# # 使用示例
# if __name__ == "__main__":
#     directory_path = input("请输入文件夹路径（默认为当前目录）: ") or "F:/25_09_21_huaweibei/源域数据集/"
#     # F:\25_09_21_huaweibei\目标域数据集
#     print(f"\n目录树: {os.path.abspath(directory_path)}")
#     print_directory_tree(directory_path)
import os
from pathlib import Path


def print_horizontal_tree(root_dir, max_depth=3, current_depth=0, parent_prefix=''):
    """
    横向打印目录树状图
    """
    if current_depth > max_depth:
        return

    try:
        items = os.listdir(root_dir)
    except PermissionError:
        return

    # 过滤掉隐藏文件并排序
    items = [item for item in items if not item.startswith('.')]
    items.sort()

    dirs = [item for item in items if os.path.isdir(os.path.join(root_dir, item))]
    files = [item for item in items if os.path.isfile(os.path.join(root_dir, item))]

    # 当前层的显示
    indent = '  ' * current_depth
    if current_depth == 0:
        print(f"📁 {os.path.basename(root_dir)}/")
    else:
        print(f"{parent_prefix}📁 {os.path.basename(root_dir)}/")

    # 显示文件
    file_prefix = '  ' * (current_depth + 1)
    for i, file in enumerate(files):
        if i == len(files) - 1 and not dirs:
            connector = '└── '
        else:
            connector = '├── '
        print(f"{parent_prefix}{file_prefix}{connector}📄 {file}")

    # 递归显示子目录
    for i, dir_name in enumerate(dirs):
        dir_path = os.path.join(root_dir, dir_name)
        if i == len(dirs) - 1:
            new_prefix = parent_prefix + '  ' * (current_depth + 1) + '    '
            connector = '└── '
        else:
            new_prefix = parent_prefix + '  ' * (current_depth + 1) + '│   '
            connector = '├── '

        print(f"{parent_prefix}{file_prefix}{connector}", end='')
        print_horizontal_tree(dir_path, max_depth, current_depth + 1, new_prefix)


def print_compact_tree(root_dir, max_depth=4):
    """
    紧凑型横向目录树
    """
    print(f"\n📂 目录结构: {os.path.abspath(root_dir)}")
    print("=" * 60)

    def _print_compact(path, depth=0, is_last=False):
        if depth > max_depth:
            return

        indent = "    " * depth
        prefix = "└── " if is_last else "├── "

        if depth == 0:
            print(f"📁 {os.path.basename(path)}/")
        else:
            print(f"{indent}{prefix}📁 {os.path.basename(path)}/")

        try:
            items = os.listdir(path)
            items = [item for item in items if not item.startswith('.')]
            items.sort()

            dirs = [item for item in items if os.path.isdir(os.path.join(path, item))]
            files = [item for item in items if os.path.isfile(os.path.join(path, item))]

            # 显示文件统计
            file_indent = "    " * (depth + 1)
            if files:
                file_list = ", ".join(files[:3])  # 只显示前3个文件
                if len(files) > 3:
                    file_list += f", ...({len(files) - 3}更多)"
                print(f"{file_indent}📄 文件: {file_list}")

            # 递归子目录
            for i, dir_name in enumerate(dirs):
                dir_path = os.path.join(path, dir_name)
                _print_compact(dir_path, depth + 1, i == len(dirs) - 1)

        except PermissionError:
            print(f"{indent}     无访问权限")
        except Exception as e:
            print(f"{indent}     读取错误: {e}")

    _print_compact(root_dir)


def print_summary_tree(root_dir):
    """
    摘要型目录结构显示
    """
    print(f"\n 目录摘要: {os.path.abspath(root_dir)}")
    print("=" * 60)

    total_dirs = 0
    total_files = 0
    mat_files = 0

    for root, dirs, files in os.walk(root_dir):
        total_dirs += len(dirs)
        total_files += len(files)
        mat_files += len([f for f in files if f.endswith('.mat')])

    print(f" 统计: {total_dirs}个文件夹, {total_files}个文件, {mat_files}个MAT文件")
    print("-" * 40)

    # 显示主要目录结构
    main_items = os.listdir(root_dir)
    main_items = [item for item in main_items if not item.startswith('.')]
    main_items.sort()

    for item in main_items:
        item_path = os.path.join(root_dir, item)
        if os.path.isdir(item_path):
            sub_items = os.listdir(item_path)
            sub_items = [sub for sub in sub_items if not sub.startswith('.')]
            sub_count = len(sub_items)
            print(f"📁 {item}/ → {sub_count}项")

            # 显示子目录的前几个
            for sub_item in sub_items[:3]:
                sub_path = os.path.join(item_path, sub_item)
                if os.path.isdir(sub_path):
                    print(f"    ├── 📁 {sub_item}/")
                else:
                    print(f"    ├── 📄 {sub_item}")

            if sub_count > 3:
                print(f"    └── ...({sub_count - 3}更多)")


# 使用示例
if __name__ == "__main__":
    directory_path = input("请输入文件夹路径（默认为源域数据集）: ") or "F:/25_09_21_huaweibei/源域数据集/"
    # F:/25_09_21_huaweibei/目标域数据集/
    print("\n" + "=" * 60)
    print("选择显示模式:")
    print("1. 详细横向树状图")
    print("2. 紧凑型目录结构")
    print("3. 摘要统计模式")

    choice = input("请选择模式 (1/2/3, 默认为2): ") or "2"

    if choice == "1":
        print("\n 详细目录树:")
        print_horizontal_tree(directory_path, max_depth=4)
    elif choice == "2":
        print_compact_tree(directory_path)
    elif choice == "3":
        print_summary_tree(directory_path)
    else:
        print_compact_tree(directory_path)

    # 额外显示MAT文件统计
    print("\n MAT文件分布:")
    print("-" * 40)
    mat_extensions = ['.mat']
    for root, dirs, files in os.walk(directory_path):
        mat_count = len([f for f in files if any(f.endswith(ext) for ext in mat_extensions)])
        if mat_count > 0:
            rel_path = os.path.relpath(root, directory_path)
            if rel_path == '.':
                rel_path = '根目录'
            print(f"{rel_path}: {mat_count}个MAT文件")
