function Q2()
    % 任务2: 源域故障诊断模型设计与评价
    fprintf('================================================================\n');
    fprintf('任务2: 源域故障诊断模型设计与评价\n');
    fprintf('================================================================\n');
    
    % 设置参数
    data_path = 'processed_data_mixed_fs';
    test_size = 0.2;
    random_seed = 42;
    k_features = 50;
    
    % 步骤1: 加载数据
    fprintf('\n加载源域特征数据（混合采样率）\n');
    fprintf('================================================================\n');
    
    feature_file = fullfile(data_path, 'extracted_features.csv');
    data_table = readtable(feature_file);
    
    fprintf('数据集大小: %d x %d\n', size(data_table));
    
    % 显示故障类型分布
    fprintf('\n故障类型分布:\n');
    [fault_types, ~, fault_idx] = unique(data_table.fault_type);
    fault_counts = accumarray(fault_idx, 1);
    for i = 1:length(fault_types)
        fprintf('  %s: %d 样本\n', fault_types{i}, fault_counts(i));
    end
    
    % 显示采样率分布（如果存在）
    if any(strcmp(data_table.Properties.VariableNames, 'original_fs'))
        fprintf('\n原始采样率分布:\n');
        [fs_types, ~, fs_idx] = unique(data_table.original_fs);
        fs_counts = accumarray(fs_idx, 1);
        for i = 1:length(fs_types)
            fprintf('  %.0f Hz: %d 样本\n', fs_types(i), fs_counts(i));
        end
    end
    
    % 步骤2: 数据预处理
    fprintf('\n数据预处理...\n');
    
    % 选择特征列
    exclude_cols = {'file_path', 'fault_type', 'sensor_type', 'original_fs', 'resampled_fs'};
    all_vars = data_table.Properties.VariableNames;
    feature_vars = setdiff(all_vars, exclude_cols);
    
    fprintf('排除列: %s\n', strjoin(exclude_cols, ', '));
    fprintf('特征列数量: %d\n', length(feature_vars));
    
    % 提取特征矩阵和标签
    X = table2array(data_table(:, feature_vars));
    y_labels = data_table.fault_type;
    
    % 标签编码
    [unique_labels, ~, y] = unique(y_labels);
    fprintf('特征维度: %d\n', size(X, 2));
    fprintf('类别数量: %d\n', length(unique_labels));
    for i = 1:length(unique_labels)
        fprintf('  %s -> %d\n', unique_labels{i}, i);
    end
    
    % 处理缺失值和异常值
    X = preprocessFeatures(X);
    
    % 步骤3: 数据集划分
    fprintf('\n数据集划分...\n');
    rng(random_seed);
    
    % 分层抽样
    [X_train, X_test, y_train, y_test, train_idx, test_idx] = stratifiedSplit(X, y, test_size);
    
    fprintf('训练集大小: %d 样本\n', size(X_train, 1));
    fprintf('测试集大小: %d 样本\n', size(X_test, 1));
    fprintf('测试集比例: %.1f%%\n', size(X_test, 1)/(size(X_train, 1) + size(X_test, 1)) * 100);
    
    % 显示类别分布
    displayClassDistribution(y_train, y_test, unique_labels);
    
    % 步骤4: 特征标准化
    fprintf('\n特征标准化...\n');
    [X_train_norm, X_test_norm, scaler_mean, scaler_std] = normalizeFeatures(X_train, X_test);
    
    % 步骤5: 特征选择
    fprintf('\n特征选择 - 方法: univariate\n');
    [X_train_selected, X_test_selected, selected_features] = selectFeatures(X_train_norm, X_test_norm, y_train, k_features);
    
    % 步骤6-8: 构建、训练和评价模型
    fprintf('\n构建和训练模型...\n');
    fprintf('================================================================\n');
    
    % 定义模型
    models = containers.Map();
    
    fprintf('训练 RandomForest 模型...\n');
    rf_model = TreeBagger(300, X_train_selected, y_train, ...
        'Method', 'classification', ...
        'NumPredictorsToSample', 'all', ...
        'MinLeafSize', 2, ...
        'OOBPredictorImportance', 'on');  
    models('RandomForest') = rf_model;
    
    % SVM
    if exist('fitcecoc', 'file')
        fprintf('训练 SVM 模型...\n');
        svm_template = templateSVM('KernelFunction', 'rbf', ...
            'KernelScale', 'auto', ...
            'BoxConstraint', 100);
        svm_model = fitcecoc(X_train_selected, y_train, ...
            'Learners', svm_template, ...
            'ClassNames', unique(y_train));
        models('SVM') = svm_model;
    end
    
    % 朴素贝叶斯
    fprintf('训练 NaiveBayes 模型...\n');
    nb_model = fitcnb(X_train_selected, y_train);
    models('NaiveBayes') = nb_model;
    
    % k-NN
    fprintf('训练 KNN 模型...\n');
    knn_model = fitcknn(X_train_selected, y_train, 'NumNeighbors', 5);
    models('KNN') = knn_model;
    
    % 步骤9: 模型评价
    fprintf('\n模型评价\n');
    fprintf('================================================================\n');
    
    results = containers.Map();
    model_names = keys(models);
    
    for i = 1:length(model_names)
        model_name = model_names{i};
        model = models(model_name);
        
        fprintf('\n评价 %s 模型:\n', model_name);
        fprintf('----------------------------------------\n');
        
        % 预测
        if isa(model, 'TreeBagger')
            [y_pred_cell, scores] = predict(model, X_test_selected);
            y_pred = cellfun(@str2double, y_pred_cell);
        else
            [y_pred, scores] = predict(model, X_test_selected);
        end
        
        % 计算指标
        accuracy = sum(y_pred == y_test) / length(y_test);
        
        % 计算混淆矩阵
        C = confusionmat(y_test, y_pred);
        
        % 计算精确率、召回率、F1分数
        [precision, recall, f1_score] = calculateMetrics(C);
        
        fprintf('准确率 (Accuracy): %.4f\n', accuracy);
        fprintf('精确率 (Precision): %.4f\n', precision);
        fprintf('召回率 (Recall): %.4f\n', recall);
        fprintf('F1分数 (F1-Score): %.4f\n', f1_score);
        
        % 保存结果
        result_struct.accuracy = accuracy;
        result_struct.precision = precision;
        result_struct.recall = recall;
        result_struct.f1_score = f1_score;
        result_struct.y_pred = y_pred;
        result_struct.confusion_matrix = C;
        result_struct.scores = scores;
        results(model_name) = result_struct;
        
        % 显示详细分类报告
        displayClassificationReport(C, unique_labels);
    end
    
    % 步骤10: 可视化结果
    fprintf('\n生成可视化结果...\n');
    
    % 绘制混淆矩阵
    plotConfusionMatrices(results, unique_labels, data_path);
    
    % 绘制模型性能对比
    plotModelComparison(results, data_path);
    
    % 特征重要性分析（仅适用于RandomForest）
    if isKey(models, 'RandomForest')
        plotFeatureImportance(models('RandomForest'), selected_features, data_path);
    end
    
    % 步骤11: 保存结果
    fprintf('\n保存评价结果...\n');
    saveResults(results, unique_labels, data_path);
    
    % 输出最佳模型
    fprintf('\n任务2完成！\n');
    best_model_name = findBestModel(results);
    best_result = results(best_model_name);
    fprintf('\n最佳模型性能:\n');
    fprintf('  模型: %s\n', best_model_name);
    fprintf('  F1分数: %.4f\n', best_result.f1_score);
    fprintf('  准确率: %.4f\n', best_result.accuracy);
end

% ========================================================================
% 辅助函数
% ========================================================================

function X_clean = preprocessFeatures(X)
    % 处理缺失值和异常值
    fprintf('检查和处理异常值...\n');
    
    % 统计异常值
    nan_count = sum(isnan(X(:)));
    inf_count = sum(isinf(X(:)));
    
    if nan_count > 0
        fprintf('发现 %d 个缺失值，用列均值填充\n', nan_count);
        % 用列均值填充缺失值
        col_means = nanmean(X, 1);
        for i = 1:size(X, 2)
            nan_idx = isnan(X(:, i));
            X(nan_idx, i) = col_means(i);
        end
    end
    
    if inf_count > 0
        fprintf('发现 %d 个无穷值，用有限值替换\n', inf_count);
        % 处理无穷值
        for i = 1:size(X, 2)
            col_data = X(:, i);
            finite_mask = isfinite(col_data);
            finite_data = col_data(finite_mask);
            
            if ~isempty(finite_data)
                max_val = max(finite_data);
                min_val = min(finite_data);
                X(isinf(col_data) & col_data > 0, i) = max_val;
                X(isinf(col_data) & col_data < 0, i) = min_val;
            end
        end
    end
    
    X_clean = X;
end

function [X_train, X_test, y_train, y_test, train_idx, test_idx] = stratifiedSplit(X, y, test_size)
    % 分层抽样划分数据集
    n_samples = length(y);
    unique_classes = unique(y);
    
    train_idx = [];
    test_idx = [];
    
    for i = 1:length(unique_classes)
        class_idx = find(y == unique_classes(i));
        n_class = length(class_idx);
        n_test = round(n_class * test_size);
        
        % 随机选择测试样本
        rand_idx = randperm(n_class);
        test_class_idx = class_idx(rand_idx(1:n_test));
        train_class_idx = class_idx(rand_idx(n_test+1:end));
        
        train_idx = [train_idx; train_class_idx];
        test_idx = [test_idx; test_class_idx];
    end
    
    X_train = X(train_idx, :);
    X_test = X(test_idx, :);
    y_train = y(train_idx);
    y_test = y(test_idx);
end

function [X_train_norm, X_test_norm, scaler_mean, scaler_std] = normalizeFeatures(X_train, X_test)
    % 特征标准化
    scaler_mean = mean(X_train, 1);
    scaler_std = std(X_train, 0, 1);
    
    % 避免除零
    scaler_std(scaler_std == 0) = 1;
    
    X_train_norm = (X_train - scaler_mean) ./ scaler_std;
    X_test_norm = (X_test - scaler_mean) ./ scaler_std;
    
    fprintf('标准化完成\n');
    fprintf('训练集特征范围: [%.3f, %.3f]\n', min(X_train_norm(:)), max(X_train_norm(:)));
    fprintf('训练集特征均值: %.3f, 标准差: %.3f\n', mean(X_train_norm(:)), std(X_train_norm(:)));
end

function [X_train_selected, X_test_selected, selected_features] = selectFeatures(X_train, X_test, y_train, k)
    % 单变量特征选择（方差分析）
    k = min(k, size(X_train, 2));
    fprintf('选择 %d 个特征（总特征数: %d）\n', k, size(X_train, 2));
    
    % 计算F统计量
    f_scores = zeros(size(X_train, 2), 1);
    for i = 1:size(X_train, 2)
        f_scores(i) = anova1(X_train(:, i), y_train, 'off');
    end
    
    % 选择前k个特征
    [~, sorted_idx] = sort(f_scores, 'descend');
    selected_features = sorted_idx(1:k);
    
    X_train_selected = X_train(:, selected_features);
    X_test_selected = X_test(:, selected_features);
    
    fprintf('特征选择完成，新特征维度: %d\n', size(X_train_selected, 2));
end

function displayClassDistribution(y_train, y_test, unique_labels)
    % 显示类别分布
    fprintf('\n训练集类别分布:\n');
    for i = 1:length(unique_labels)
        count = sum(y_train == i);
        percentage = count / length(y_train) * 100;
        fprintf('  %s: %d 样本 (%.1f%%)\n', unique_labels{i}, count, percentage);
    end
    
    fprintf('\n测试集类别分布:\n');
    for i = 1:length(unique_labels)
        count = sum(y_test == i);
        percentage = count / length(y_test) * 100;
        fprintf('  %s: %d 样本 (%.1f%%)\n', unique_labels{i}, count, percentage);
    end
end

function [precision, recall, f1_score] = calculateMetrics(C)
    % 计算分类指标
    n_classes = size(C, 1);
    precision_per_class = zeros(n_classes, 1);
    recall_per_class = zeros(n_classes, 1);
    f1_per_class = zeros(n_classes, 1);
    
    for i = 1:n_classes
        tp = C(i, i);
        fp = sum(C(:, i)) - tp;
        fn = sum(C(i, :)) - tp;
        
        if (tp + fp) > 0
            precision_per_class(i) = tp / (tp + fp);
        end
        
        if (tp + fn) > 0
            recall_per_class(i) = tp / (tp + fn);
        end
        
        if (precision_per_class(i) + recall_per_class(i)) > 0
            f1_per_class(i) = 2 * precision_per_class(i) * recall_per_class(i) / ...
                (precision_per_class(i) + recall_per_class(i));
        end
    end
    
    % 加权平均
    class_support = sum(C, 2);
    total_support = sum(class_support);
    
    precision = sum(precision_per_class .* class_support) / total_support;
    recall = sum(recall_per_class .* class_support) / total_support;
    f1_score = sum(f1_per_class .* class_support) / total_support;
end

function displayClassificationReport(C, unique_labels)
    % 显示详细分类报告
    fprintf('\n详细分类报告:\n');
    fprintf('%-15s %10s %10s %10s %10s\n', 'Class', 'Precision', 'Recall', 'F1-Score', 'Support');
    fprintf('%-15s %10s %10s %10s %10s\n', '-----', '---------', '------', '--------', '-------');
    
    n_classes = size(C, 1);
    for i = 1:n_classes
        tp = C(i, i);
        fp = sum(C(:, i)) - tp;
        fn = sum(C(i, :)) - tp;
        support = sum(C(i, :));
        
        precision = tp / (tp + fp);
        recall = tp / (tp + fn);
        f1 = 2 * precision * recall / (precision + recall);
        
        if isnan(precision), precision = 0; end
        if isnan(recall), recall = 0; end
        if isnan(f1), f1 = 0; end
        
        fprintf('%-15s %10.3f %10.3f %10.3f %10d\n', ...
            unique_labels{i}, precision, recall, f1, support);
    end
end

function plotConfusionMatrices(results, unique_labels, data_path)
    % 绘制混淆矩阵（修改：使用MATLAB内置颜色映射）
    model_names = keys(results);
    n_models = length(model_names);
    
    % 计算子图布局
    n_cols = min(3, n_models);
    n_rows = ceil(n_models / n_cols);
    
    figure('Position', [100, 100, 300*n_cols, 300*n_rows]);
    
    for i = 1:n_models
        model_name = model_names{i};
        result = results(model_name);
        
        subplot(n_rows, n_cols, i);
        
        % 绘制混淆矩阵热图
        imagesc(result.confusion_matrix);
        colorbar;
        % 创建自定义蓝色渐变

        custom_blue = [1 1 1; 0.9 0.95 1; 0.8 0.9 1; 0.6 0.8 1; 0.4 0.7 1; 0.2 0.5 1; 0 0.3 1];
        colormap(custom_blue);
        
        % 添加数值标注
        for row = 1:size(result.confusion_matrix, 1)
            for col = 1:size(result.confusion_matrix, 2)
                val = result.confusion_matrix(row, col);
                if val > max(result.confusion_matrix(:)) * 0.5
                    text_color = 'white';
                else
                    text_color = 'black';
                end
                text(col, row, sprintf('%d', val), ...
                    'HorizontalAlignment', 'center', 'FontSize', 10, ...
                    'Color', text_color, 'FontWeight', 'bold');
            end
        end
        
        title(sprintf('%s\nAccuracy: %.3f', model_name, result.accuracy));
        xlabel('Predicted');
        ylabel('Actual');
        
        % 设置坐标轴标签
        set(gca, 'XTick', 1:length(unique_labels), 'XTickLabel', unique_labels, ...
            'YTick', 1:length(unique_labels), 'YTickLabel', unique_labels);
        xtickangle(45);
    end
    
    sgtitle('混淆矩阵');
    
    % 保存图片
    if ~exist(data_path, 'dir')
        mkdir(data_path);
    end
    saveas(gcf, fullfile(data_path, 'confusion_matrices.png'));
    fprintf('混淆矩阵已保存\n');
end

function plotModelComparison(results, data_path)
    % 绘制模型性能对比
    model_names = keys(results);
    n_models = length(model_names);
    
    metrics = {'accuracy', 'precision', 'recall', 'f1_score'};
    metric_labels = {'Accuracy', 'Precision', 'Recall', 'F1-Score'};
    
    % 准备数据
    performance_data = zeros(n_models, length(metrics));
    for i = 1:n_models
        result = results(model_names{i});
        performance_data(i, :) = [result.accuracy, result.precision, result.recall, result.f1_score];
    end
    
    figure('Position', [100, 100, 800, 600]);
    
    % 绘制分组条形图
    bar(performance_data);
    
    xlabel('模型');
    ylabel('分数');
    title('模型性能对比');
    legend(metric_labels, 'Location', 'best');
    
    set(gca, 'XTickLabel', model_names);
    xtickangle(45);
    ylim([0, 1.1]);
    grid on;
    
    % 保存图片
    saveas(gcf, fullfile(data_path, 'model_comparison.png'));
    fprintf('模型对比图已保存\n');
end

function plotFeatureImportance(rf_model, selected_features, data_path)
    % 绘制特征重要性（修改：添加错误处理）
    fprintf('分析RandomForest特征重要性...\n');
    
    try
        % 尝试获取特征重要性
        importance = rf_model.OOBPermutedPredictorDeltaError;
        
        if isempty(importance)
            fprintf('警告: 特征重要性数据为空，跳过特征重要性分析\n');
            return;
        end
        
    catch ME
        fprintf('警告: 无法获取特征重要性数据: %s\n', ME.message);
        fprintf('请确保在训练时设置了 OOBPredictorImportance\n');
        return;
    end
    
    % 检查重要性数据的有效性
    if all(importance == 0) || all(isnan(importance))
        fprintf('警告: 特征重要性均为零或NaN，跳过可视化\n');
        return;
    end
    
    % 选择前20个重要特征
    n_features = min(20, length(importance));
    [sorted_importance, sort_idx] = sort(importance, 'descend');
    top_importance = sorted_importance(1:n_features);
    top_features = sort_idx(1:n_features);
    
    % 创建特征名称
    if nargin < 2 || isempty(selected_features)
        feature_names = arrayfun(@(x) sprintf('Feature_%d', x), top_features, 'UniformOutput', false);
    else
        feature_names = arrayfun(@(x) sprintf('Feature_%d', selected_features(x)), top_features, 'UniformOutput', false);
    end
    
    figure('Position', [100, 100, 800, 600]);
    barh(1:n_features, top_importance, 'FaceColor', [0.2 0.6 0.8]);
    
    xlabel('重要性分数');
    ylabel('特征');
    title(sprintf('RandomForest 特征重要性 (Top %d)', n_features));
    
    % 设置y轴标签
    set(gca, 'YTick', 1:n_features, 'YTickLabel', feature_names, 'YDir', 'reverse');
    
    % 添加数值标签
    for i = 1:n_features
        text(top_importance(i) + max(top_importance)*0.01, i, sprintf('%.4f', top_importance(i)), ...
            'VerticalAlignment', 'middle', 'FontSize', 8);
    end
    
    grid on;
    
    % 保存图片
    if ~exist(data_path, 'dir')
        mkdir(data_path);
    end
    saveas(gcf, fullfile(data_path, 'feature_importance.png'));
    fprintf('特征重要性图已保存\n');
end

function saveResults(results, unique_labels, data_path)
    % 保存评价结果
    model_names = keys(results);
    n_models = length(model_names);
    
    % 创建结果表格
    Model = model_names';
    Accuracy = zeros(n_models, 1);
    Precision = zeros(n_models, 1);
    Recall = zeros(n_models, 1);
    F1_Score = zeros(n_models, 1);
    
    for i = 1:n_models
        result = results(model_names{i});
        Accuracy(i) = result.accuracy;
        Precision(i) = result.precision;
        Recall(i) = result.recall;
        F1_Score(i) = result.f1_score;
    end
    
    results_table = table(Model, Accuracy, Precision, Recall, F1_Score);
    
    % 保存结果表格
    if ~exist(data_path, 'dir')
        mkdir(data_path);
    end
    writetable(results_table, fullfile(data_path, 'model_evaluation_results.csv'));
    
    fprintf('结果已保存到 %s 目录\n', data_path);
end

function best_model_name = findBestModel(results)
    % 根据F1分数找出最佳模型
    model_names = keys(results);
    best_f1 = 0;
    best_model_name = model_names{1};
    
    for i = 1:length(model_names)
        result = results(model_names{i});
        if result.f1_score > best_f1
            best_f1 = result.f1_score;
            best_model_name = model_names{i};
        end
    end
end