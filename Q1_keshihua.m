%% 清理环境
clear; clc; close all;

fprintf('================================================================================\n');
fprintf('轴承故障诊断系统 - MATLAB版本\n');
fprintf('================================================================================\n');

source_data_path = 'F:\25_09_21_huaweibei\源域数据集';

if ~exist(source_data_path, 'dir')
    fprintf('警告: 指定路径不存在，使用当前目录\n');
    source_data_path = pwd;
end

fprintf('数据路径: %s\n', source_data_path);

try
    %% 步骤1: 数据筛选和参数设置
    fprintf('\n步骤1: 数据筛选和参数设置...\n');
    
    % 处理参数
    params = struct();
    params.source_data_path = source_data_path;
    params.window_size = 4096;
    params.overlap_ratio = 0.5;
    params.target_fs = 32000;  % 目标采样率32kHz
    
    % 轴承参数
    bearing_params = containers.Map();
    bearing_params('SKF6205') = struct('n', 9, 'd', 0.3126, 'D', 1.537);
    bearing_params('SKF6203') = struct('n', 9, 'd', 0.2656, 'D', 1.122);
    params.bearing_params = bearing_params;
    
    % 选择源域文件
    selected_files = select_source_data();
    
    %% 步骤2: 特征提取
    fprintf('\n步骤2: 特征提取...\n');
    all_features = process_all_files(selected_files, params);
    
    %% 步骤3: 数据分析
    fprintf('\n步骤3: 数据分析...\n');
    analyze_features(all_features);
    
    %% 步骤4: 保存结果
    fprintf('\n步骤4: 保存结果...\n');
    save_results(all_features);
    
    fprintf('\n================================================================================\n');
    fprintf('故障诊断系统运行完成!\n');
    fprintf('================================================================================\n');
    
catch ME
    fprintf('程序执行出错: %s\n', ME.message);
    fprintf('错误位置: %s (行 %d)\n', ME.stack(1).name, ME.stack(1).line);
end

%% ======================== 核心函数定义 ========================

function selected_files = select_source_data()
    % 筛选源域数据
    fprintf('筛选源域数据...\n');
    
    selected_files = struct();
    selected_files.Normal = {};
    selected_files.OR = {};
    selected_files.IR = {};
    selected_files.B = {};
    
    % 正常状态：48kHz数据
    normal_files = {
        struct('path', '48kHz_Normal_data/N_0.mat', 'fs', 48000);
        struct('path', '48kHz_Normal_data/N_1_(1772rpm).mat', 'fs', 48000);
        struct('path', '48kHz_Normal_data/N_2_(1750rpm).mat', 'fs', 48000);
        struct('path', '48kHz_Normal_data/N_3.mat', 'fs', 48000)
    };
    selected_files.Normal = normal_files;
    
    % 外圈故障：混合采样率
    or_files = {};
    
    % 48kHz外圈故障
    or_48k_paths = {
        '48kHz_DE_data/OR/Centered/0007/OR007@6_0.mat';
        '48kHz_DE_data/OR/Centered/0007/OR007@6_1.mat';
        '48kHz_DE_data/OR/Centered/0014/OR014@6_0.mat';
        '48kHz_DE_data/OR/Centered/0014/OR014@6_1.mat';
        '48kHz_DE_data/OR/Centered/0021/OR021@6_0.mat';
        '48kHz_DE_data/OR/Centered/0021/OR021@6_1.mat'
    };
    
    for i = 1:length(or_48k_paths)
        or_files{end+1} = struct('path', or_48k_paths{i}, 'fs', 48000);
    end
    
    % 12kHz外圈故障
    or_12k_paths = {
        '12kHz_DE_data/OR/Centered/0007/OR007@6_0.mat';
        '12kHz_DE_data/OR/Centered/0007/OR007@6_1.mat';
        '12kHz_DE_data/OR/Centered/0014/OR014@6_0.mat';
        '12kHz_DE_data/OR/Centered/0014/OR014@6_1.mat'
    };
    
    for i = 1:length(or_12k_paths)
        or_files{end+1} = struct('path', or_12k_paths{i}, 'fs', 12000);
    end
    selected_files.OR = or_files;
    
    % 内圈故障：混合采样率
    ir_files = {};
    
    % 48kHz内圈故障
    ir_48k_paths = {
        '48kHz_DE_data/IR/0007/IR007_0.mat';
        '48kHz_DE_data/IR/0007/IR007_1.mat';
        '48kHz_DE_data/IR/0014/IR014_0.mat';
        '48kHz_DE_data/IR/0014/IR014_1.mat'
    };
    
    for i = 1:length(ir_48k_paths)
        ir_files{end+1} = struct('path', ir_48k_paths{i}, 'fs', 48000);
    end
    
    % 12kHz内圈故障
    ir_12k_paths = {
        '12kHz_DE_data/IR/0007/IR007_0.mat';
        '12kHz_DE_data/IR/0007/IR007_1.mat';
        '12kHz_DE_data/IR/0014/IR014_0.mat';
        '12kHz_DE_data/IR/0014/IR014_1.mat'
    };
    
    for i = 1:length(ir_12k_paths)
        ir_files{end+1} = struct('path', ir_12k_paths{i}, 'fs', 12000);
    end
    selected_files.IR = ir_files;
    
    % 滚动体故障：混合采样率
    b_files = {};
    
    % 48kHz滚动体故障
    b_48k_paths = {
        '48kHz_DE_data/B/0007/B007_0.mat';
        '48kHz_DE_data/B/0007/B007_1.mat';
        '48kHz_DE_data/B/0014/B014_0.mat';
        '48kHz_DE_data/B/0014/B014_1.mat'
    };
    
    for i = 1:length(b_48k_paths)
        b_files{end+1} = struct('path', b_48k_paths{i}, 'fs', 48000);
    end
    
    % 12kHz滚动体故障
    b_12k_paths = {
        '12kHz_DE_data/B/0007/B007_0.mat';
        '12kHz_DE_data/B/0007/B007_1.mat';
        '12kHz_DE_data/B/0014/B014_0.mat';
        '12kHz_DE_data/B/0014/B014_1.mat'
    };
    
    for i = 1:length(b_12k_paths)
        b_files{end+1} = struct('path', b_12k_paths{i}, 'fs', 12000);
    end
    selected_files.B = b_files;
    
    % 打印选择结果
    fprintf('数据选择完成:\n');
    fault_types = fieldnames(selected_files);
    total_files = 0;
    
    for i = 1:length(fault_types)
        fault_type = fault_types{i};
        files = selected_files.(fault_type);
        fs_48k = sum(cellfun(@(x) x.fs == 48000, files));
        fs_12k = sum(cellfun(@(x) x.fs == 12000, files));
        fprintf('  %s: %d个文件 (48kHz:%d, 12kHz:%d)\n', fault_type, length(files), fs_48k, fs_12k);
        total_files = total_files + length(files);
    end
    fprintf('  总计: %d个文件\n', total_files);
end

function all_features = process_all_files(selected_files, params)
    % 处理所有文件并提取特征
    fprintf('开始特征提取:\n');
    fprintf('  目标采样率: %d Hz\n', params.target_fs);
    fprintf('  窗口大小: %d, 重叠率: %.1f\n', params.window_size, params.overlap_ratio);
    
    all_features = {};
    fault_types = fieldnames(selected_files);
    
    for f = 1:length(fault_types)
        fault_type = fault_types{f};
        file_list = selected_files.(fault_type);
        
        fprintf('\n处理 %s 故障类型 (%d个文件)...\n', fault_type, length(file_list));
        fault_sample_count = 0;
        
        for i = 1:length(file_list)
            file_info = file_list{i};
            file_path = file_info.path;
            original_fs = file_info.fs;
            
            fprintf('  处理文件: %s (采样率: %d Hz)\n', file_path, original_fs);
            
            % 加载MAT文件
            mat_data = load_mat_file(file_path, params.source_data_path);
            
            if isempty(mat_data)
                fprintf('    跳过: 文件加载失败\n');
                continue;
            end
            
            % 提取DE信号
            if ~isempty(mat_data.DE)
                signal_data = mat_data.DE;
                rpm = mat_data.RPM;
                
                % 重采样
                if original_fs ~= params.target_fs
                    signal_data = resample_signal(signal_data, original_fs, params.target_fs);
                end
                
                % 分割信号
                samples = extract_signal_samples(signal_data, params.window_size, params.overlap_ratio);
                
                fprintf('    提取了 %d 个样本窗口\n', length(samples));
                
                % 提取每个样本的特征
                for j = 1:length(samples)
                    sample = samples{j};
                    
                    % 检查信号质量
                    if ~check_signal_quality(sample)
                        continue;
                    end
                    
                    % 提取特征
                    features = extract_all_features(sample, params.target_fs, rpm, ...
                        fault_type, file_path, original_fs, params.bearing_params);
                    
                    all_features{end+1} = features;
                    fault_sample_count = fault_sample_count + 1;
                end
            end
        end
        
        fprintf('  %s 总样本数: %d\n', fault_type, fault_sample_count);
    end
    
    fprintf('\n特征提取完成，总样本数: %d\n', length(all_features));
end

function mat_data = load_mat_file(file_path, source_data_path)
    % 加载MAT文件
    full_path = fullfile(source_data_path, file_path);
    mat_data = struct('DE', [], 'FE', [], 'BA', [], 'RPM', 1797);
    
    if ~exist(full_path, 'file')
        fprintf('    文件不存在: %s\n', full_path);
        mat_data = [];
        return;
    end
    
    try
        loaded_data = load(full_path);
        field_names = fieldnames(loaded_data);
        
        for i = 1:length(field_names)
            field_name = field_names{i};
            
            if contains(field_name, 'DE_time') || strcmp(field_name, 'X097_DE_time')
                mat_data.DE = loaded_data.(field_name)(:);
            elseif contains(field_name, 'FE_time')
                mat_data.FE = loaded_data.(field_name)(:);
            elseif contains(field_name, 'BA_time')
                mat_data.BA = loaded_data.(field_name)(:);
            elseif contains(field_name, 'RPM')
                rpm_data = loaded_data.(field_name);
                if ~isempty(rpm_data)
                    mat_data.RPM = rpm_data(1);
                end
            end
        end
        
        if isempty(mat_data.DE)
            % 尝试其他可能的字段名
            for i = 1:length(field_names)
                field_name = field_names{i};
                data = loaded_data.(field_name);
                if isnumeric(data) && length(data) > 10000  % 假设信号数据长度 > 10k
                    mat_data.DE = data(:);
                    break;
                end
            end
        end
        
    catch ME
        fprintf('    加载文件失败: %s\n', ME.message);
        mat_data = [];
    end
end

function resampled_data = resample_signal(signal_data, original_fs, target_fs)
    % 重采样信号
    if original_fs == target_fs
        resampled_data = signal_data;
        return;
    end
    
    try
        % 计算重采样比例
        [p, q] = rat(target_fs / original_fs, 0.0001);
        resampled_data = resample(signal_data, p, q);
    catch
        % 如果resample函数不可用，使用简单的插值
        t_original = (0:length(signal_data)-1) / original_fs;
        t_new = (0:1/target_fs:t_original(end));
        resampled_data = interp1(t_original, signal_data, t_new, 'linear', 'extrap');
        resampled_data = resampled_data(:);
    end
end

function samples = extract_signal_samples(signal_data, window_size, overlap_ratio)
    % 从长信号中提取样本窗口
    step_size = round(window_size * (1 - overlap_ratio));
    samples = {};
    
    for start_idx = 1:step_size:(length(signal_data) - window_size + 1)
        end_idx = start_idx + window_size - 1;
        sample = signal_data(start_idx:end_idx);
        samples{end+1} = sample;
    end
end

function is_good = check_signal_quality(signal_data)
    % 检查信号质量
    is_good = true;
    
    % 检查是否有无效值
    if any(~isfinite(signal_data))
        is_good = false;
        return;
    end
    
    % 检查方差是否太小
    if std(signal_data) < 1e-8
        is_good = false;
        return;
    end
    
    % 检查是否有极端值
    if max(abs(signal_data)) > 100 * std(signal_data)
        is_good = false;
        return;
    end
end

function features = extract_all_features(signal_data, fs, rpm, fault_type, file_path, original_fs, bearing_params)
    % 提取完整特征集
    features = struct();
    
    % 基本信息
    features.fault_type = fault_type;
    features.file_path = file_path;
    features.rpm = rpm;
    features.original_fs = original_fs;
    features.resampled_fs = fs;
    
    % 数据预处理
    signal_data = signal_data - mean(signal_data);  % 去均值
    
    % 时域特征
    time_features = extract_time_features(signal_data);
    freq_features = extract_freq_features(signal_data, fs, rpm, bearing_params);
    time_freq_features = extract_time_freq_features(signal_data, fs);
    
    % 合并所有特征
    features = merge_features(features, time_features, 'time');
    features = merge_features(features, freq_features, 'freq');
    features = merge_features(features, time_freq_features, 'tf');
end

function time_features = extract_time_features(signal_data)
    % 提取时域特征
    time_features = struct();
    
    % 基本统计特征
    time_features.mean = mean(signal_data);
    time_features.std = std(signal_data);
    time_features.var = var(signal_data);
    time_features.rms = sqrt(mean(signal_data.^2));
    time_features.peak = max(abs(signal_data));
    time_features.peak_to_peak = range(signal_data);
    time_features.skewness = skewness(signal_data);
    time_features.kurtosis = kurtosis(signal_data);
    
    % 形态因子
    mean_abs = mean(abs(signal_data));
    if mean_abs > 0 && time_features.rms > 0
        time_features.crest_factor = time_features.peak / time_features.rms;
        time_features.impulse_factor = time_features.peak / mean_abs;
        time_features.shape_factor = time_features.rms / mean_abs;
    else
        time_features.crest_factor = 0;
        time_features.impulse_factor = 0;
        time_features.shape_factor = 0;
    end
end

function freq_features = extract_freq_features(signal_data, fs, rpm, bearing_params)
    % 提取频域特征
    freq_features = struct();
    
    % FFT计算
    N = length(signal_data);
    Y = fft(signal_data);
    f = (0:N/2-1) * fs / N;
    magnitude = abs(Y(1:N/2));
    
    % 频谱特征
    total_power = sum(magnitude);
    if total_power > 0
        freq_features.spectral_centroid = sum(f .* magnitude') / total_power;
        freq_features.spectral_variance = sum((f - freq_features.spectral_centroid).^2 .* magnitude') / total_power;
    else
        freq_features.spectral_centroid = 0;
        freq_features.spectral_variance = 0;
    end
    
    % 故障特征频率
    fault_freqs = get_fault_frequencies(rpm, bearing_params);
    
    % 查找各故障频率处的幅值
    fault_names = fieldnames(fault_freqs);
    for i = 1:length(fault_names)
        fault_name = fault_names{i};
        target_freq = fault_freqs.(fault_name);
        
        if target_freq > 0 && target_freq < fs/2
            [~, idx] = min(abs(f - target_freq));
            % 在目标频率附近搜索峰值
            search_range = max(5, round(length(f) * 0.01));  % 搜索范围
            start_idx = max(1, idx - search_range);
            end_idx = min(length(magnitude), idx + search_range);
            freq_features.([fault_name '_amplitude']) = max(magnitude(start_idx:end_idx));
        else
            freq_features.([fault_name '_amplitude']) = 0;
        end
    end
    
    % 频段能量比
    total_energy = sum(magnitude.^2);
    if total_energy > 0
        low_freq_idx = f <= 500;
        mid_freq_idx = (f > 500) & (f <= 5000);
        high_freq_idx = f > 5000;
        
        freq_features.low_freq_ratio = sum(magnitude(low_freq_idx).^2) / total_energy;
        freq_features.mid_freq_ratio = sum(magnitude(mid_freq_idx).^2) / total_energy;
        freq_features.high_freq_ratio = sum(magnitude(high_freq_idx).^2) / total_energy;
    else
        freq_features.low_freq_ratio = 0;
        freq_features.mid_freq_ratio = 0;
        freq_features.high_freq_ratio = 0;
    end
end

function time_freq_features = extract_time_freq_features(signal_data, fs)
    % 提取时频域特征
    time_freq_features = struct();
    
    try
        % 小波分解 (如果有Wavelet Toolbox)
        if exist('wavedec', 'file')
            [c, l] = wavedec(signal_data, 4, 'db4');
            
            % 各层能量
            for i = 1:5
                if i == 1
                    coeffs = appcoef(c, l, 'db4', i-1);
                else
                    coeffs = detcoef(c, l, i-1);
                end
                time_freq_features.(sprintf('level_%d_energy', i)) = sum(coeffs.^2);
            end
        else
            % 简单的频带分析
            N = length(signal_data);
            Y = fft(signal_data);
            f = (0:N/2-1) * fs / N;
            magnitude = abs(Y(1:N/2));
            
            % 分频段计算能量
            bands = [0 100; 100 500; 500 2000; 2000 8000; 8000 fs/2];
            for i = 1:size(bands, 1)
                band_idx = (f >= bands(i,1)) & (f < bands(i,2));
                time_freq_features.(sprintf('band_%d_energy', i)) = sum(magnitude(band_idx).^2);
            end
        end
        
        % 包络分析
        if exist('hilbert', 'file')
            envelope = abs(hilbert(signal_data));
            time_freq_features.envelope_mean = mean(envelope);
            time_freq_features.envelope_std = std(envelope);
        else
            time_freq_features.envelope_mean = 0;
            time_freq_features.envelope_std = 0;
        end
        
    catch ME
        fprintf('    时频特征提取失败: %s\n', ME.message);
        % 设置默认值
        for i = 1:5
            time_freq_features.(sprintf('level_%d_energy', i)) = 0;
        end
        time_freq_features.envelope_mean = 0;
        time_freq_features.envelope_std = 0;
    end
end

function fault_freqs = get_fault_frequencies(rpm, bearing_params)
    % 计算故障特征频率
    if bearing_params.isKey('SKF6205')
        params = bearing_params('SKF6205');
    else
        % 默认参数
        params = struct('n', 9, 'd', 0.3126, 'D', 1.537);
    end
    
    fr = rpm / 60;  % 转频
    bpfo = fr * (params.n / 2) * (1 - params.d / params.D);  % 外圈故障频率
    bpfi = fr * (params.n / 2) * (1 + params.d / params.D);  % 内圈故障频率
    bsf = fr * (params.D / params.d) * (1 - (params.d / params.D)^2);  % 滚动体故障频率
    
    fault_freqs = struct('BPFO', bpfo, 'BPFI', bpfi, 'BSF', bsf, 'FR', fr);
end

function merged_features = merge_features(base_features, new_features, prefix)
    % 合并特征结构体
    merged_features = base_features;
    field_names = fieldnames(new_features);
    
    for i = 1:length(field_names)
        field_name = field_names{i};
        new_field_name = [prefix '_' field_name];
        merged_features.(new_field_name) = new_features.(field_name);
    end
end

function analyze_features(all_features)
    % 分析提取的特征
    fprintf('特征分析:\n');
    
    if isempty(all_features)
        fprintf('  没有特征数据可分析\n');
        return;
    end
    
    % 统计各类别样本数
    fault_counts = containers.Map();
    fs_counts = containers.Map();
    
    for i = 1:length(all_features)
        features = all_features{i};
        
        % 故障类型统计
        fault_type = features.fault_type;
        if fault_counts.isKey(fault_type)
            fault_counts(fault_type) = fault_counts(fault_type) + 1;
        else
            fault_counts(fault_type) = 1;
        end
        
        % 采样率统计
        fs_key = sprintf('%d', features.original_fs);
        if fs_counts.isKey(fs_key)
            fs_counts(fs_key) = fs_counts(fs_key) + 1;
        else
            fs_counts(fs_key) = 1;
        end
    end
    
    % 打印统计结果
    fprintf('  故障类型分布:\n');
    fault_keys = keys(fault_counts);
    for i = 1:length(fault_keys)
        fault_type = fault_keys{i};
        count = fault_counts(fault_type);
        percentage = count / length(all_features) * 100;
        fprintf('    %s: %d (%.1f%%)\n', fault_type, count, percentage);
    end
    
    fprintf('  采样率分布:\n');
    fs_keys = keys(fs_counts);
    for i = 1:length(fs_keys)
        fs_key = fs_keys{i};
        count = fs_counts(fs_key);
        percentage = count / length(all_features) * 100;
        fprintf('    %s Hz: %d (%.1f%%)\n', fs_key, count, percentage);
    end
    
    % 创建简单的可视化
    create_visualizations(all_features, fault_counts);
end

function create_visualizations(all_features, fault_counts)
    % 创建可视化图表
    try
        figure('Position', [100, 100, 1200, 800]);
        
        % 故障类型分布饼图
        subplot(2, 2, 1);
        fault_keys = keys(fault_counts);
        fault_values = cell2mat(values(fault_counts));
        pie(fault_values, fault_keys);
        title('故障类型分布');
        
        % 特征对比图
        subplot(2, 2, 2);
        plot_feature_comparison(all_features, 'time_rms');
        
        subplot(2, 2, 3);
        plot_feature_comparison(all_features, 'time_kurtosis');
        
        subplot(2, 2, 4);
        plot_feature_comparison(all_features, 'freq_spectral_centroid');
        
        sgtitle('轴承故障特征分析');
        
        fprintf('  可视化图表已生成\n');
        
    catch ME
        fprintf('  可视化失败: %s\n', ME.message);
    end
end

function plot_feature_comparison(all_features, feature_name)
    % 绘制特征对比图
    fault_types = {'Normal', 'OR', 'IR', 'B'};
    colors = {'b', 'r', 'g', 'm'};
    
    hold on;
    for i = 1:length(fault_types)
        fault_type = fault_types{i};
        feature_values = [];
        
        for j = 1:length(all_features)
            features = all_features{j};
            if strcmp(features.fault_type, fault_type) && isfield(features, feature_name)
                feature_values(end+1) = features.(feature_name);
            end
        end
        
        if ~isempty(feature_values)
            histogram(feature_values, 20, 'FaceColor', colors{i}, 'FaceAlpha', 0.6, ...
                'DisplayName', fault_type);
        end
    end
    
    xlabel(strrep(feature_name, '_', ' '));
    ylabel('频数');
    title(['特征对比: ' strrep(feature_name, '_', ' ')]);
    legend('show');
    grid on;
    hold off;
end

function save_results(all_features)
    % 保存结果
    if isempty(all_features)
        fprintf('  没有结果可保存\n');
        return;
    end
    
    try
        % 保存MAT文件
        save('bearing_diagnosis_features.mat', 'all_features');
        fprintf('  特征数据已保存: bearing_diagnosis_features.mat\n');
        
        % 尝试保存CSV文件
        save_features_csv(all_features);
        
    catch ME
        fprintf('  保存失败: %s\n', ME.message);
    end
end

function save_features_csv(all_features)
    % 保存特征数据为CSV格式
    try
        if isempty(all_features)
            return;
        end
        
        % 获取所有字段名
        all_fields = fieldnames(all_features{1});
        
        % 创建数据矩阵
        data_cell = cell(length(all_features), length(all_fields));
        
        for i = 1:length(all_features)
            features = all_features{i};
            for j = 1:length(all_fields)
                field_name = all_fields{j};
                if isfield(features, field_name)
                    value = features.(field_name);
                    if isnumeric(value) && isscalar(value)
                        data_cell{i, j} = value;
                    elseif ischar(value) || isstring(value)
                        data_cell{i, j} = char(value);
                    else
                        data_cell{i, j} = '';
                    end
                else
                    data_cell{i, j} = NaN;
                end
            end
        end
        
        % 创建表格并保存
        feature_table = cell2table(data_cell, 'VariableNames', all_fields);
        writetable(feature_table, 'bearing_diagnosis_features.csv');
        fprintf('  CSV文件已保存: bearing_diagnosis_features.csv\n');
        
    catch ME
        fprintf('  CSV保存失败: %s\n', ME.message);
    end
end