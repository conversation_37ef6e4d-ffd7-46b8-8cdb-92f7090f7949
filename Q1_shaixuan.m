%% 轴承故障诊断系统 - 完整MATLAB版本（混合采样率 + 异常值处理）
% 基于Python原版转换
% 功能: 12kHz+48kHz重采样、特征提取、异常值检测
% 使用方法: 修改数据路径后直接运行

%% 清理环境和初始设置
clear; clc; close all;
warning('off', 'all');  % 关闭警告

%% 主程序
fprintf('================================================================================\n');
fprintf('任务1: 源域数据筛选与特征提取（混合采样率版本）\n');
fprintf('================================================================================\n');

source_data_path = 'F:\25_09_21_huaweibei\源域数据集';

% 检查路径
if ~exist(source_data_path, 'dir')
    fprintf('警告: 指定路径不存在，使用当前目录: %s\n', pwd);
    source_data_path = pwd;
end

fprintf('数据路径: %s\n', source_data_path);

try
    %% 步骤1: 初始化处理器参数
    fprintf('\n步骤1: 初始化处理器参数...\n');
    processor = initialize_processor(source_data_path);
    
    %% 步骤2: 筛选源域数据
    fprintf('\n步骤2: 筛选源域数据（混合12kHz和48kHz）...\n');
    selected_files = select_source_data();
    
    %% 步骤3: 特征提取（包含重采样）
    fprintf('\n步骤3: 特征提取（包含重采样）...\n');
    all_features = process_all_selected_files(selected_files, processor);
    
    %% 步骤4: 分类别异常值处理
    fprintf('\n步骤4: 分类别异常值处理...\n');
    cleaned_features = remove_outliers_by_category(all_features);
    
    %% 步骤5: 保存处理后的数据
    fprintf('\n步骤5: 保存数据...\n');
    output_dir = save_processed_data(cleaned_features, processor);
    
    fprintf('\n================================================================================\n');
    fprintf('任务1混合采样率版本完成！所有数据已保存到: %s\n', output_dir);
    fprintf('================================================================================\n');
    
    fprintf('\n关键改进:\n');
    fprintf('1. 同时使用12kHz和48kHz数据，增强模型泛化能力\n');
    fprintf('2. 统一重采样到32kHz，与目标域采样率一致\n');
    fprintf('3. 多采样率数据融合，学习采样率无关的故障特征\n');
    fprintf('4. 分类别异常值检测与清理，保护小样本类别\n');
    
catch ME
    fprintf('程序执行出错: %s\n', ME.message);
    fprintf('错误位置: %s (行 %d)\n', ME.stack(1).name, ME.stack(1).line);
    rethrow(ME);
end

%% ======================== 核心函数定义 ========================

function processor = initialize_processor(source_data_path)
    % 初始化处理器参数
    processor = struct();
    processor.source_data_path = source_data_path;
    processor.window_size = 4096;
    processor.overlap_ratio = 0.5;
    processor.target_fs = 32000;  % 目标采样率32kHz
    
    % 轴承参数
    processor.bearing_params = containers.Map();
    processor.bearing_params('SKF6205') = struct('n', 9, 'd', 0.3126, 'D', 1.537);
    processor.bearing_params('SKF6203') = struct('n', 9, 'd', 0.2656, 'D', 1.122);
    
    fprintf('处理器参数:\n');
    fprintf('  窗口大小: %d\n', processor.window_size);
    fprintf('  重叠率: %.1f\n', processor.overlap_ratio);
    fprintf('  目标采样率: %d Hz\n', processor.target_fs);
end

function selected_files = select_source_data()
    % 筛选源域数据，包含12kHz和48kHz数据
    selected_files = struct();
    selected_files.Normal = {};
    selected_files.OR = {};
    selected_files.IR = {};
    selected_files.B = {};
    
    % 正常状态：48kHz数据（全部4个文件）
    normal_files_48k = {
        struct('path', '48kHz_Normal_data/N_0.mat', 'fs', 48000);
        struct('path', '48kHz_Normal_data/N_1_(1772rpm).mat', 'fs', 48000);
        struct('path', '48kHz_Normal_data/N_2_(1750rpm).mat', 'fs', 48000);
        struct('path', '48kHz_Normal_data/N_3.mat', 'fs', 48000)
    };
    selected_files.Normal = normal_files_48k;
    
    % 外圈故障：混合12kHz和48kHz数据（全部文件）
    or_files = {};
    
    % 48kHz外圈故障数据（全部可用文件）
    or_48k_paths = {
        % Centered位置
        '48kHz_DE_data/OR/Centered/0007/OR007@6_0.mat';
        '48kHz_DE_data/OR/Centered/0007/OR007@6_1.mat';
        '48kHz_DE_data/OR/Centered/0007/OR007@6_2.mat';
        '48kHz_DE_data/OR/Centered/0007/OR007@6_3.mat';
        '48kHz_DE_data/OR/Centered/0014/OR014@6_0.mat';
        '48kHz_DE_data/OR/Centered/0014/OR014@6_1.mat';
        '48kHz_DE_data/OR/Centered/0014/OR014@6_2.mat';
        '48kHz_DE_data/OR/Centered/0014/OR014@6_3.mat';
        '48kHz_DE_data/OR/Centered/0021/OR021@6_0.mat';
        '48kHz_DE_data/OR/Centered/0021/OR021@6_1.mat';
        '48kHz_DE_data/OR/Centered/0021/OR021@6_2.mat';
        '48kHz_DE_data/OR/Centered/0021/OR021@6_3.mat';
        % Opposite位置
        '48kHz_DE_data/OR/Opposite/0007/OR007@12_0.mat';
        '48kHz_DE_data/OR/Opposite/0007/OR007@12_1.mat';
        '48kHz_DE_data/OR/Opposite/0007/OR007@12_2.mat';
        '48kHz_DE_data/OR/Opposite/0007/OR007@12_3.mat';
        '48kHz_DE_data/OR/Opposite/0021/OR021@12_0.mat';
        '48kHz_DE_data/OR/Opposite/0021/OR021@12_1.mat';
        '48kHz_DE_data/OR/Opposite/0021/OR021@12_2.mat';
        '48kHz_DE_data/OR/Opposite/0021/OR021@12_3.mat';
        % Orthogonal位置
        '48kHz_DE_data/OR/Orthogonal/0007/OR007@3_0.mat';
        '48kHz_DE_data/OR/Orthogonal/0007/OR007@3_1.mat';
        '48kHz_DE_data/OR/Orthogonal/0007/OR007@3_2.mat';
        '48kHz_DE_data/OR/Orthogonal/0007/OR007@3_3.mat';
        '48kHz_DE_data/OR/Orthogonal/0021/OR021@3_0.mat';
        '48kHz_DE_data/OR/Orthogonal/0021/OR021@3_1.mat';
        '48kHz_DE_data/OR/Orthogonal/0021/OR021@3_2.mat';
        '48kHz_DE_data/OR/Orthogonal/0021/OR021@3_3.mat'
    };
    for i = 1:length(or_48k_paths)
        or_files{end+1} = struct('path', or_48k_paths{i}, 'fs', 48000);
    end
    
    % 12kHz外圈故障数据（全部可用文件）
    or_12k_paths = {
        % Centered位置
        '12kHz_DE_data/OR/Centered/0007/OR007@6_0.mat';
        '12kHz_DE_data/OR/Centered/0007/OR007@6_1.mat';
        '12kHz_DE_data/OR/Centered/0007/OR007@6_2.mat';
        '12kHz_DE_data/OR/Centered/0007/OR007@6_3.mat';
        '12kHz_DE_data/OR/Centered/0014/OR014@6_0.mat';
        '12kHz_DE_data/OR/Centered/0014/OR014@6_1.mat';
        '12kHz_DE_data/OR/Centered/0014/OR014@6_2.mat';
        '12kHz_DE_data/OR/Centered/0014/OR014@6_3.mat';
        '12kHz_DE_data/OR/Centered/0021/OR021@6_0.mat';
        '12kHz_DE_data/OR/Centered/0021/OR021@6_1.mat';
        '12kHz_DE_data/OR/Centered/0021/OR021@6_2.mat';
        '12kHz_DE_data/OR/Centered/0021/OR021@6_3.mat';
        % Opposite位置
        '12kHz_DE_data/OR/Opposite/0007/OR007@12_0.mat';
        '12kHz_DE_data/OR/Opposite/0007/OR007@12_1.mat';
        '12kHz_DE_data/OR/Opposite/0007/OR007@12_2.mat';
        '12kHz_DE_data/OR/Opposite/0007/OR007@12_3.mat';
        '12kHz_DE_data/OR/Opposite/0021/OR021@12_0.mat';
        '12kHz_DE_data/OR/Opposite/0021/OR021@12_1.mat';
        '12kHz_DE_data/OR/Opposite/0021/OR021@12_2.mat';
        '12kHz_DE_data/OR/Opposite/0021/OR021@12_3.mat';
        % Orthogonal位置
        '12kHz_DE_data/OR/Orthogonal/0007/OR007@3_0.mat';
        '12kHz_DE_data/OR/Orthogonal/0007/OR007@3_1.mat';
        '12kHz_DE_data/OR/Orthogonal/0007/OR007@3_2.mat';
        '12kHz_DE_data/OR/Orthogonal/0007/OR007@3_3.mat';
        '12kHz_DE_data/OR/Orthogonal/0021/OR021@3_0.mat';
        '12kHz_DE_data/OR/Orthogonal/0021/OR021@3_1.mat';
        '12kHz_DE_data/OR/Orthogonal/0021/OR021@3_2.mat';
        '12kHz_DE_data/OR/Orthogonal/0021/OR021@3_3.mat'
    };
    for i = 1:length(or_12k_paths)
        or_files{end+1} = struct('path', or_12k_paths{i}, 'fs', 12000);
    end
    selected_files.OR = or_files;
    
    % 内圈故障：混合采样率（全部文件）
    ir_files = {};
    
    % 48kHz内圈故障数据（全部可用文件）
    ir_48k_paths = {
        '48kHz_DE_data/IR/0007/IR007_0.mat';
        '48kHz_DE_data/IR/0007/IR007_1.mat';
        '48kHz_DE_data/IR/0007/IR007_2.mat';
        '48kHz_DE_data/IR/0007/IR007_3.mat';
        '48kHz_DE_data/IR/0014/IR014_0.mat';
        '48kHz_DE_data/IR/0014/IR014_1.mat';
        '48kHz_DE_data/IR/0014/IR014_2.mat';
        '48kHz_DE_data/IR/0014/IR014_3.mat';
        '48kHz_DE_data/IR/0021/IR021_0.mat';
        '48kHz_DE_data/IR/0021/IR021_1.mat';
        '48kHz_DE_data/IR/0021/IR021_2.mat';
        '48kHz_DE_data/IR/0021/IR021_3.mat'
    };
    for i = 1:length(ir_48k_paths)
        ir_files{end+1} = struct('path', ir_48k_paths{i}, 'fs', 48000);
    end
    
    % 12kHz内圈故障数据（全部可用文件）
    ir_12k_paths = {
        '12kHz_DE_data/IR/0007/IR007_0.mat';
        '12kHz_DE_data/IR/0007/IR007_1.mat';
        '12kHz_DE_data/IR/0007/IR007_2.mat';
        '12kHz_DE_data/IR/0007/IR007_3.mat';
        '12kHz_DE_data/IR/0014/IR014_0.mat';
        '12kHz_DE_data/IR/0014/IR014_1.mat';
        '12kHz_DE_data/IR/0014/IR014_2.mat';
        '12kHz_DE_data/IR/0014/IR014_3.mat';
        '12kHz_DE_data/IR/0021/IR021_0.mat';
        '12kHz_DE_data/IR/0021/IR021_1.mat';
        '12kHz_DE_data/IR/0021/IR021_2.mat';
        '12kHz_DE_data/IR/0021/IR021_3.mat';
        '12kHz_DE_data/IR/0028/IR028_0_(1797rpm).mat';
        '12kHz_DE_data/IR/0028/IR028_1_(1772rpm).mat';
        '12kHz_DE_data/IR/0028/IR028_2_(1750rpm).mat';
        '12kHz_DE_data/IR/0028/IR028_3_(1730rpm).mat'
    };
    for i = 1:length(ir_12k_paths)
        ir_files{end+1} = struct('path', ir_12k_paths{i}, 'fs', 12000);
    end
    selected_files.IR = ir_files;
    
    % 滚动体故障：混合采样率（全部文件）
    b_files = {};
    
    % 48kHz滚动体故障数据（全部可用文件）
    b_48k_paths = {
        '48kHz_DE_data/B/0007/B007_0.mat';
        '48kHz_DE_data/B/0007/B007_1.mat';
        '48kHz_DE_data/B/0007/B007_2.mat';
        '48kHz_DE_data/B/0007/B007_3.mat';
        '48kHz_DE_data/B/0014/B014_0.mat';
        '48kHz_DE_data/B/0014/B014_1.mat';
        '48kHz_DE_data/B/0014/B014_2.mat';
        '48kHz_DE_data/B/0014/B014_3.mat';
        '48kHz_DE_data/B/0021/B021_0.mat';
        '48kHz_DE_data/B/0021/B021_1.mat';
        '48kHz_DE_data/B/0021/B021_2.mat';
        '48kHz_DE_data/B/0021/B021_3.mat'
    };
    for i = 1:length(b_48k_paths)
        b_files{end+1} = struct('path', b_48k_paths{i}, 'fs', 48000);
    end
    
    % 12kHz滚动体故障数据（全部可用文件）
    b_12k_paths = {
        '12kHz_DE_data/B/0007/B007_0.mat';
        '12kHz_DE_data/B/0007/B007_1.mat';
        '12kHz_DE_data/B/0007/B007_2.mat';
        '12kHz_DE_data/B/0007/B007_3.mat';
        '12kHz_DE_data/B/0014/B014_0.mat';
        '12kHz_DE_data/B/0014/B014_1.mat';
        '12kHz_DE_data/B/0014/B014_2.mat';
        '12kHz_DE_data/B/0014/B014_3.mat';
        '12kHz_DE_data/B/0021/B021_0.mat';
        '12kHz_DE_data/B/0021/B021_1.mat';
        '12kHz_DE_data/B/0021/B021_2.mat';
        '12kHz_DE_data/B/0021/B021_3.mat';
        '12kHz_DE_data/B/0028/B028_0_(1797rpm).mat';
        '12kHz_DE_data/B/0028/B028_1_(1772rpm).mat';
        '12kHz_DE_data/B/0028/B028_2_(1750rpm).mat';
        '12kHz_DE_data/B/0028/B028_3_(1730rpm).mat'
    };
    for i = 1:length(b_12k_paths)
        b_files{end+1} = struct('path', b_12k_paths{i}, 'fs', 12000);
    end
    selected_files.B = b_files;
    
    % 打印筛选结果
    fprintf('================================================================================\n');
    fprintf('源域数据筛选结果（混合采样率）\n');
    fprintf('================================================================================\n');
    
    fault_types = fieldnames(selected_files);
    total_files = 0;
    
    for i = 1:length(fault_types)
        fault_type = fault_types{i};
        files = selected_files.(fault_type);
        fprintf('\n%s: %d 个文件\n', fault_type, length(files));
        
        fs_48k = sum(cellfun(@(x) x.fs == 48000, files));
        fs_12k = sum(cellfun(@(x) x.fs == 12000, files));
        fprintf('  - 48kHz: %d 个文件\n', fs_48k);
        fprintf('  - 12kHz: %d 个文件\n', fs_12k);
        
        total_files = total_files + length(files);
    end
    
    fprintf('\n总计: %d 个文件\n', total_files);
end

function all_features = process_all_selected_files(selected_files, processor)
    % 处理所有选定的文件（包含重采样）
    fprintf('================================================================================\n');
    fprintf('开始特征提取（混合采样率 + 重采样）\n');
    fprintf('================================================================================\n');
    fprintf('目标采样率: %d Hz\n', processor.target_fs);
    fprintf('窗口参数: 窗口大小=%d, 重叠率=%.1f\n', processor.window_size, processor.overlap_ratio);
    
    all_features = {};
    total_samples = 0;
    
    fault_types = fieldnames(selected_files);
    
    for f = 1:length(fault_types)
        fault_type = fault_types{f};
        file_list = selected_files.(fault_type);
        
        fprintf('\n处理 %s 故障类型...\n', fault_type);
        fault_samples = 0;
        
        for i = 1:length(file_list)
            file_info = file_list{i};
            file_path = file_info.path;
            original_fs = file_info.fs;
            
            fprintf('  处理文件: %s (原始采样率: %d Hz)\n', file_path, original_fs);
            
            % 加载MAT文件
            mat_data = load_mat_file(file_path, processor.source_data_path);
            if isempty(mat_data)
                continue;
            end
            
            rpm = mat_data.RPM;
            
            % 处理DE数据（主要传感器）
            if ~isempty(mat_data.DE)
                signal_length = length(mat_data.DE);
                fprintf('    原始信号长度: %d 点 (%.2f 秒)\n', ...
                    signal_length, signal_length / original_fs);
                
                % 重采样到目标采样率
                resampled_signal = resample_signal(mat_data.DE, original_fs, processor.target_fs);
                fprintf('    重采样后长度: %d 点 (%.2f 秒)\n', ...
                    length(resampled_signal), length(resampled_signal) / processor.target_fs);
                
                % 分割信号为多个样本
                samples = extract_samples_from_signal(resampled_signal, processor.window_size, processor.overlap_ratio);
                valid_samples = 0;
                
                for j = 1:length(samples)
                    sample = samples{j};
                    
                    % 检查样本质量
                    if ~check_signal_quality(sample)
                        continue;
                    end
                    
                    file_info_dict = struct(...
                        'file_path', sprintf('%s_sample_%d', file_path, j), ...
                        'fault_type', fault_type, ...
                        'original_fs', original_fs);
                    
                    % 提取特征
                    features = extract_features(sample, 'DE', processor.target_fs, rpm, file_info_dict, processor.bearing_params);
                    all_features{end+1} = features;
                    valid_samples = valid_samples + 1;
                end
                
                fprintf('    从DE信号中提取了 %d 个有效样本\n', valid_samples);
                fault_samples = fault_samples + valid_samples;
            end
        end
        
        fprintf('  %s 总样本数: %d\n', fault_type, fault_samples);
        total_samples = total_samples + fault_samples;
    end
    
    fprintf('\n特征提取完成:\n');
    fprintf('  总样本数: %d\n', length(all_features));
    
    % 打印各类别样本统计
    if ~isempty(all_features)
        fprintf('\n各类别样本数:\n');
        fault_counts = containers.Map();
        fs_counts = containers.Map();
        
        for i = 1:length(all_features)
            feat = all_features{i};
            
            % 统计故障类型
            if fault_counts.isKey(feat.fault_type)
                fault_counts(feat.fault_type) = fault_counts(feat.fault_type) + 1;
            else
                fault_counts(feat.fault_type) = 1;
            end
            
            % 统计采样率
            fs_key = sprintf('%d', feat.original_fs);
            if fs_counts.isKey(fs_key)
                fs_counts(fs_key) = fs_counts(fs_key) + 1;
            else
                fs_counts(fs_key) = 1;
            end
        end
        
        fault_types = {'Normal', 'OR', 'IR', 'B'};
        for i = 1:length(fault_types)
            fault_type = fault_types{i};
            if fault_counts.isKey(fault_type)
                count = fault_counts(fault_type);
            else
                count = 0;
            end
            fprintf('    %s: %d\n', fault_type, count);
        end
        
        fprintf('\n原始采样率分布:\n');
        fs_keys = keys(fs_counts);
        for i = 1:length(fs_keys)
            fs_key = fs_keys{i};
            count = fs_counts(fs_key);
            fprintf('    %s Hz: %d\n', fs_key, count);
        end
    end
end

function mat_data = load_mat_file(file_path, source_data_path)
    % 加载MAT文件
    full_path = fullfile(source_data_path, file_path);
    mat_data = struct('DE', [], 'FE', [], 'BA', [], 'RPM', 1797);
    
    if ~exist(full_path, 'file')
        mat_data = [];
        return;
    end
    
    try
        loaded_data = load(full_path);
        field_names = fieldnames(loaded_data);
        
        for i = 1:length(field_names)
            field_name = field_names{i};
            
            if contains(field_name, 'DE_time') || strcmp(field_name, 'X097_DE_time')
                mat_data.DE = loaded_data.(field_name)(:);
            elseif contains(field_name, 'FE_time')
                mat_data.FE = loaded_data.(field_name)(:);
            elseif contains(field_name, 'BA_time')
                mat_data.BA = loaded_data.(field_name)(:);
            elseif contains(field_name, 'RPM')
                rpm_data = loaded_data.(field_name);
                if ~isempty(rpm_data)
                    mat_data.RPM = rpm_data(1);
                end
            end
        end
        
        % 如果没找到DE数据，尝试查找其他数值字段
        if isempty(mat_data.DE)
            for i = 1:length(field_names)
                field_name = field_names{i};
                if ~startsWith(field_name, '_')
                    data = loaded_data.(field_name);
                    if isnumeric(data) && length(data) > 10000
                        mat_data.DE = data(:);
                        break;
                    end
                end
            end
        end
        
    catch ME
        fprintf('    加载文件失败: %s\n', ME.message);
        mat_data = [];
    end
end

function resampled_signal = resample_signal(signal_data, original_fs, target_fs)
    % 重采样信号到目标采样率
    if original_fs == target_fs
        resampled_signal = signal_data;
        return;
    end
    
    try
        % 计算重采样比例
        if original_fs > target_fs
            % 降采样
            up = target_fs;
            down = original_fs;
            % 简化分数
            gcd_val = gcd(up, down);
            up = up / gcd_val;
            down = down / gcd_val;
        else
            % 升采样
            up = target_fs;
            down = original_fs;
            gcd_val = gcd(up, down);
            up = up / gcd_val;
            down = down / gcd_val;
        end
        
        % 使用MATLAB的resample函数
        resampled_signal = resample(signal_data, up, down);
        
    catch ME
        % 如果resample失败，使用简单的插值
        fprintf('    使用插值重采样: %s\n', ME.message);
        t_original = (0:length(signal_data)-1) / original_fs;
        t_new = 0:(1/target_fs):t_original(end);
        resampled_signal = interp1(t_original, signal_data, t_new, 'linear', 'extrap');
        resampled_signal = resampled_signal(:);
    end
end

function samples = extract_samples_from_signal(signal_data, window_size, overlap_ratio)
    % 将长信号分割成多个样本窗口
    step_size = round(window_size * (1 - overlap_ratio));
    samples = {};
    
    for start_idx = 1:step_size:(length(signal_data) - window_size + 1)
        end_idx = start_idx + window_size - 1;
        sample = signal_data(start_idx:end_idx);
        samples{end+1} = sample;
    end
end

function is_good = check_signal_quality(signal_data)
    % 检查信号质量
    is_good = true;
    
    % 检查是否有无效值
    if any(~isfinite(signal_data))
        is_good = false;
        return;
    end
    
    % 检查方差是否太小
    signal_std = std(signal_data);
    if signal_std < 1e-8
        is_good = false;
        return;
    end
    
    % 检查是否有极端值
    signal_max = max(abs(signal_data));
    if signal_max > 100 * signal_std
        is_good = false;
        return;
    end
end

function features = extract_features(signal_data, sensor_type, fs, rpm, file_info, bearing_params)
    % 提取完整特征集
    features = struct();
    
    % 添加文件信息
    features.file_path = file_info.file_path;
    features.fault_type = file_info.fault_type;
    features.sensor_type = sensor_type;
    features.rpm = rpm;
    features.original_fs = file_info.original_fs;
    features.resampled_fs = fs;
    
    % 数据预处理
    signal_data = signal_data - mean(signal_data);
    
    % 时域特征
    time_features = extract_time_domain_features(signal_data);
    freq_features = extract_frequency_domain_features(signal_data, fs, rpm, bearing_params);
    time_freq_features = extract_time_frequency_features(signal_data, fs);
    
    % 合并特征
    time_fields = fieldnames(time_features);
    for i = 1:length(time_fields)
        field_name = time_fields{i};
        features.([sensor_type '_' field_name]) = time_features.(field_name);
    end
    
    freq_fields = fieldnames(freq_features);
    for i = 1:length(freq_fields)
        field_name = freq_fields{i};
        features.([sensor_type '_' field_name]) = freq_features.(field_name);
    end
    
    tf_fields = fieldnames(time_freq_features);
    for i = 1:length(tf_fields)
        field_name = tf_fields{i};
        features.([sensor_type '_' field_name]) = time_freq_features.(field_name);
    end
end

function features = extract_time_domain_features(signal_data)
    % 提取时域特征
    features = struct();
    
    % 基本统计特征
    features.mean = mean(signal_data);
    features.std = std(signal_data);
    features.var = var(signal_data);
    features.rms = sqrt(mean(signal_data.^2));
    features.peak = max(abs(signal_data));
    features.peak_to_peak = range(signal_data);
    
    % 高阶统计特征
    features.skewness = skewness(signal_data);
    features.kurtosis = kurtosis(signal_data);
    
    % 形态特征
    mean_abs = mean(abs(signal_data));
    if mean_abs > 0
        features.crest_factor = features.peak / features.rms;
        features.impulse_factor = features.peak / mean_abs;
        features.shape_factor = features.rms / mean_abs;
        sqrt_mean_sqrt = mean(sqrt(abs(signal_data)));
        if sqrt_mean_sqrt > 0
            features.clearance_factor = features.peak / (sqrt_mean_sqrt^2);
        else
            features.clearance_factor = 0;
        end
    else
        features.crest_factor = 0;
        features.impulse_factor = 0;
        features.shape_factor = 0;
        features.clearance_factor = 0;
    end
end

function features = extract_frequency_domain_features(signal_data, fs, rpm, bearing_params)
    % 提取频域特征（修复版本）
    features = struct();
    
    % FFT变换
    N = length(signal_data);
    fft_vals = fft(signal_data);
    freqs = (0:N/2-1) * fs / N;
    magnitude = abs(fft_vals(1:N/2));
    
    % 防止除零错误
    total_magnitude = sum(magnitude);
    if total_magnitude == 0
        features.spectral_centroid = 0;
        features.spectral_variance = 0;
        features.spectral_skewness = 0;
        features.spectral_kurtosis = 0;
        features.BPFO_amplitude = 0;
        features.BPFI_amplitude = 0;
        features.BSF_amplitude = 0;
        features.FR_amplitude = 0;
        features.low_freq_energy_ratio = 0;
        features.mid_freq_energy_ratio = 0;
        features.high_freq_energy_ratio = 0;
        return;
    end
    
    % 频谱特征
    features.spectral_centroid = sum(freqs .* magnitude') / total_magnitude;
    spectral_variance = sum(((freqs - features.spectral_centroid).^2) .* magnitude') / total_magnitude;
    features.spectral_variance = spectral_variance;
    
    if spectral_variance > 0
        features.spectral_skewness = sum(((freqs - features.spectral_centroid).^3) .* magnitude') / ...
            (total_magnitude * spectral_variance^1.5);
        features.spectral_kurtosis = sum(((freqs - features.spectral_centroid).^4) .* magnitude') / ...
            (total_magnitude * spectral_variance^2);
    else
        features.spectral_skewness = 0;
        features.spectral_kurtosis = 0;
    end
    
    % 故障特征频率分析
    try
        fault_freqs = get_fault_frequencies(rpm, bearing_params);
        fault_names = fieldnames(fault_freqs);
        
        freq_resolution = fs / N;
        
        for i = 1:length(fault_names)
            fault_name = fault_names{i};
            fault_freq = fault_freqs.(fault_name);
            
            if fault_freq > 0 && fault_freq < fs/2
                % 找到最接近的频率索引
                [~, freq_idx] = min(abs(freqs - fault_freq));
                
                % 计算搜索范围
                freq_range_hz = max(5.0, fault_freq * 0.05);
                freq_range_points = max(10, round(freq_range_hz / freq_resolution));
                
                start_idx = max(1, freq_idx - floor(freq_range_points/2));
                end_idx = min(length(magnitude), freq_idx + floor(freq_range_points/2));
                
                if end_idx > start_idx
                    search_magnitudes = magnitude(start_idx:end_idx);
                    max_amplitude = max(search_magnitudes);
                    features.([fault_name '_amplitude']) = max_amplitude;
                else
                    features.([fault_name '_amplitude']) = 0;
                end
            else
                features.([fault_name '_amplitude']) = 0;
            end
        end
        
    catch ME
        fprintf('故障特征频率计算失败: %s, RPM: %d\n', ME.message, rpm);
        features.BPFO_amplitude = 0;
        features.BPFI_amplitude = 0;
        features.BSF_amplitude = 0;
        features.FR_amplitude = 0;
    end
    
    % 频带能量比
    total_energy = sum(magnitude.^2);
    if total_energy > 0
        % 低频段 (0-500Hz)
        low_freq_idx = freqs <= 500;
        features.low_freq_energy_ratio = sum(magnitude(low_freq_idx).^2) / total_energy;
        
        % 中频段 (500-5000Hz)
        mid_freq_idx = (freqs > 500) & (freqs <= 5000);
        features.mid_freq_energy_ratio = sum(magnitude(mid_freq_idx).^2) / total_energy;
        
        % 高频段 (5000Hz以上)
        high_freq_idx = freqs > 5000;
        features.high_freq_energy_ratio = sum(magnitude(high_freq_idx).^2) / total_energy;
    else
        features.low_freq_energy_ratio = 0;
        features.mid_freq_energy_ratio = 0;
        features.high_freq_energy_ratio = 0;
    end
end

function fault_freqs = get_fault_frequencies(rpm, bearing_params)
    % 计算轴承故障特征频率
    if bearing_params.isKey('SKF6205')
        params = bearing_params('SKF6205');
    else
        params = struct('n', 9, 'd', 0.3126, 'D', 1.537);
    end
    
    fr = rpm / 60;
    bpfo = fr * (params.n / 2) * (1 - params.d / params.D);
    bpfi = fr * (params.n / 2) * (1 + params.d / params.D);
    bsf = fr * (params.D / params.d) * (1 - (params.d / params.D)^2);
    
    fault_freqs = struct('BPFO', bpfo, 'BPFI', bpfi, 'BSF', bsf, 'FR', fr);
end

function features = extract_time_frequency_features(signal_data, fs)
    % 提取时频域特征
    features = struct();
    
    try
        % 小波包分解（如果有小波工具箱）
        if exist('wpdec', 'file')
            wavelet = 'db4';
            levels = 4;
            wpt = wpdec(signal_data, levels, wavelet);
            
            % 提取各频带能量
            energy_features = zeros(2^levels, 1);
            for i = 1:2^levels
                try
                    node_coeffs = wpcoef(wpt, [levels, i-1]);
                    energy_features(i) = sum(node_coeffs.^2);
                catch
                    energy_features(i) = 0;
                end
            end
            
            total_energy = sum(energy_features);
            if total_energy > 0
                for i = 1:length(energy_features)
                    features.(sprintf('wavelet_energy_band_%d', i-1)) = energy_features(i) / total_energy;
                end
                
                % 小波包熵
                energy_ratios = energy_features / total_energy;
                energy_ratios = energy_ratios(energy_ratios > 0);
                if ~isempty(energy_ratios)
                    features.wavelet_entropy = -sum(energy_ratios .* log2(energy_ratios + 1e-12));
                else
                    features.wavelet_entropy = 0;
                end
            else
                for i = 1:2^levels
                    features.(sprintf('wavelet_energy_band_%d', i-1)) = 0;
                end
                features.wavelet_entropy = 0;
            end
        else
            % 简单的频带分析作为替代
            N = length(signal_data);
            Y = fft(signal_data);
            f = (0:N/2-1) * fs / N;
            magnitude = abs(Y(1:N/2));
            
            % 分频段计算能量
            bands = [0 100; 100 500; 500 2000; 2000 8000; 8000 fs/2];
            total_energy = sum(magnitude.^2);
            
            for i = 1:size(bands, 1)
                band_idx = (f >= bands(i,1)) & (f < bands(i,2));
                band_energy = sum(magnitude(band_idx).^2);
                if total_energy > 0
                    features.(sprintf('wavelet_energy_band_%d', i-1)) = band_energy / total_energy;
                else
                    features.(sprintf('wavelet_energy_band_%d', i-1)) = 0;
                end
            end
            
            % 补充剩余频段
            for i = size(bands, 1)+1:16
                features.(sprintf('wavelet_energy_band_%d', i-1)) = 0;
            end
            
            features.wavelet_entropy = 0;
        end
        
        % 包络分析
        if exist('hilbert', 'file')
            analytic_signal = hilbert(signal_data);
            envelope = abs(analytic_signal);
            
            features.envelope_mean = mean(envelope);
            features.envelope_std = std(envelope);
            features.envelope_skewness = skewness(envelope);
            features.envelope_kurtosis = kurtosis(envelope);
        else
            features.envelope_mean = 0;
            features.envelope_std = 0;
            features.envelope_skewness = 0;
            features.envelope_kurtosis = 0;
        end
        
    catch ME
        fprintf('时频特征提取失败: %s\n', ME.message);
        for i = 1:16
            features.(sprintf('wavelet_energy_band_%d', i-1)) = 0;
        end
        features.wavelet_entropy = 0;
        features.envelope_mean = 0;
        features.envelope_std = 0;
        features.envelope_skewness = 0;
        features.envelope_kurtosis = 0;
    end
end

function cleaned_features = remove_outliers_by_category(all_features)
    % 分类别移除异常值
    if isempty(all_features)
        fprintf('没有特征数据可处理\n');
        cleaned_features = all_features;
        return;
    end
    
    fprintf('================================================================================\n');
    fprintf('分类别异常值检测与移除\n');
    fprintf('================================================================================\n');
    
    method = 'both';  % 使用IQR和Z-score两种方法
    iqr_factor = 1.5;
    z_threshold = 3;
    max_removal_ratio = 0.1;
    
    fprintf('检测方法: %s\n', method);
    fprintf('IQR倍数因子: %.1f\n', iqr_factor);
    fprintf('Z-score阈值: %d\n', z_threshold);
    fprintf('每类最大移除比例: %.1f%%\n', max_removal_ratio * 100);
    
    % 转换为矩阵和标签用于分析
    [feature_matrix, feature_names, labels, indices_by_type] = convert_to_matrix(all_features);
    
    fprintf('\n原始数据分布:\n');
    fault_types = {'Normal', 'OR', 'IR', 'B'};
    original_counts = containers.Map();
    
    for i = 1:length(fault_types)
        fault_type = fault_types{i};
        if indices_by_type.isKey(fault_type)
            count = length(indices_by_type(fault_type));
        else
            count = 0;
        end
        original_counts(fault_type) = count;
        fprintf('  %s: %d\n', fault_type, count);
    end
    
    % 检测异常值
    outlier_indices = detect_outliers_by_category_matrix(feature_matrix, labels, indices_by_type, method, iqr_factor, z_threshold);
    
    % 应用移除比例限制并移除异常值
    indices_to_remove = [];
    removal_summary = containers.Map();
    
    for i = 1:length(fault_types)
        fault_type = fault_types{i};
        
        if outlier_indices.isKey(fault_type)
            outliers = outlier_indices(fault_type);
        else
            outliers = [];
        end
        
        original_count = original_counts(fault_type);
        
        if original_count == 0
            removal_summary(fault_type) = struct('removed', 0, 'remaining', 0, 'removal_ratio', 0);
            continue;
        end
        
        % 计算允许移除的最大数量
        max_removal = round(original_count * max_removal_ratio);
        
        % 如果异常值太多，随机选择一部分移除
        if length(outliers) > max_removal
            fprintf('  %s: 检测到%d个异常值，限制移除%d个\n', fault_type, length(outliers), max_removal);
            outliers = outliers(randperm(length(outliers), max_removal));
        end
        
        indices_to_remove = [indices_to_remove; outliers];
        remaining_count = original_count - length(outliers);
        removal_ratio = length(outliers) / original_count;
        
        removal_summary(fault_type) = struct(...
            'removed', length(outliers), ...
            'remaining', remaining_count, ...
            'removal_ratio', removal_ratio);
    end
    
    % 移除异常值
    keep_indices = setdiff(1:length(all_features), indices_to_remove);
    cleaned_features = all_features(keep_indices);
    
    % 打印移除结果
    fprintf('\n异常值移除结果:\n');
    fprintf('%-8s %-8s %-8s %-8s %-8s\n', '类别', '原始', '移除', '剩余', '移除率');
    fprintf('%s\n', repmat('-', 1, 50));
    
    total_original = 0;
    total_removed = 0;
    total_remaining = 0;
    
    for i = 1:length(fault_types)
        fault_type = fault_types{i};
        summary = removal_summary(fault_type);
        original = original_counts(fault_type);
        
        fprintf('%-8s %-8d %-8d %-8d %6.1f%%\n', ...
            fault_type, original, summary.removed, summary.remaining, summary.removal_ratio * 100);
        
        total_original = total_original + original;
        total_removed = total_removed + summary.removed;
        total_remaining = total_remaining + summary.remaining;
    end
    
    overall_removal_ratio = total_removed / total_original;
    
    fprintf('%s\n', repmat('-', 1, 50));
    fprintf('%-8s %-8d %-8d %-8d %6.1f%%\n', ...
        '总计', total_original, total_removed, total_remaining, overall_removal_ratio * 100);
    
    fprintf('\n数据清理完成:\n');
    fprintf('  原始样本数: %d\n', total_original);
    fprintf('  移除样本数: %d\n', total_removed);
    fprintf('  保留样本数: %d\n', total_remaining);
    fprintf('  整体移除率: %.2f%%\n', overall_removal_ratio * 100);
end

function [feature_matrix, feature_names, labels, indices_by_type] = convert_to_matrix(all_features)
    % 将特征结构体转换为矩阵格式以便分析
    if isempty(all_features)
        feature_matrix = [];
        feature_names = {};
        labels = {};
        indices_by_type = containers.Map();
        return;
    end
    
    % 获取特征名称（排除非数值字段）
    all_field_names = fieldnames(all_features{1});
    exclude_fields = {'file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs'};
    feature_names = setdiff(all_field_names, exclude_fields);
    
    % 构建特征矩阵
    n_samples = length(all_features);
    n_features = length(feature_names);
    feature_matrix = zeros(n_samples, n_features);
    labels = cell(n_samples, 1);
    
    for i = 1:n_samples
        features = all_features{i};
        labels{i} = features.fault_type;
        
        for j = 1:n_features
            field_name = feature_names{j};
            if isfield(features, field_name)
                value = features.(field_name);
                if isnumeric(value) && isscalar(value) && isfinite(value)
                    feature_matrix(i, j) = value;
                else
                    feature_matrix(i, j) = 0;
                end
            else
                feature_matrix(i, j) = 0;
            end
        end
    end
    
    % 按故障类型分组索引
    indices_by_type = containers.Map();
    fault_types = {'Normal', 'OR', 'IR', 'B'};
    
    for i = 1:length(fault_types)
        fault_type = fault_types{i};
        type_indices = find(strcmp(labels, fault_type));
        indices_by_type(fault_type) = type_indices;
    end
    
    fprintf('用于异常值检测的特征列数: %d\n', n_features);
end

function outlier_indices = detect_outliers_by_category_matrix(feature_matrix, labels, indices_by_type, method, iqr_factor, z_threshold)
    % 基于矩阵的分类别异常值检测
    outlier_indices = containers.Map();
    fault_types = {'Normal', 'OR', 'IR', 'B'};
    
    for i = 1:length(fault_types)
        fault_type = fault_types{i};
        
        if ~indices_by_type.isKey(fault_type)
            outlier_indices(fault_type) = [];
            continue;
        end
        
        type_indices = indices_by_type(fault_type);
        if isempty(type_indices)
            outlier_indices(fault_type) = [];
            continue;
        end
        
        fprintf('\n检测 %s 类别异常值 (样本数: %d)...\n', fault_type, length(type_indices));
        
        category_data = feature_matrix(type_indices, :);
        category_outliers = [];
        
        if strcmp(method, 'iqr') || strcmp(method, 'both')
            % IQR方法检测异常值
            for j = 1:size(category_data, 2)
                col_data = category_data(:, j);
                if std(col_data) > 1e-8  % 避免处理常数列
                    Q1 = prctile(col_data, 25);
                    Q3 = prctile(col_data, 75);
                    IQR = Q3 - Q1;
                    
                    if IQR > 0
                        lower_bound = Q1 - iqr_factor * IQR;
                        upper_bound = Q3 + iqr_factor * IQR;
                        
                        outlier_mask = (col_data < lower_bound) | (col_data > upper_bound);
                        local_outliers = find(outlier_mask);
                        global_outliers = type_indices(local_outliers);
                        category_outliers = union(category_outliers, global_outliers);
                    end
                end
            end
        end
        
        if strcmp(method, 'zscore') || strcmp(method, 'both')
            % Z-score方法检测异常值
            z_scores = abs(zscore(category_data));
            outlier_mask = any(z_scores > z_threshold, 2);
            local_outliers = find(outlier_mask);
            global_outliers = type_indices(local_outliers);
            category_outliers = union(category_outliers, global_outliers);
        end
        
        outlier_indices(fault_type) = category_outliers;
        fprintf('  检测到 %d 个异常值 (%.2f%%)\n', ...
            length(category_outliers), length(category_outliers) / length(type_indices) * 100);
    end
end

function output_dir = save_processed_data(features_data, processor)
    % 保存处理后的数据
    output_dir = 'processed_data_mixed_fs';
    
    if ~exist(output_dir, 'dir')
        mkdir(output_dir);
    end
    
    if isempty(features_data)
        fprintf('没有特征数据可保存\n');
        return;
    end
    
    try
        % 保存为MAT文件
        save(fullfile(output_dir, 'extracted_features.mat'), 'features_data');
        
        % 转换为表格并保存CSV
        feature_table = convert_features_to_table(features_data);
        writetable(feature_table, fullfile(output_dir, 'extracted_features.csv'));
        
        % 保存处理参数
        processing_params = struct();
        processing_params.window_size = processor.window_size;
        processing_params.overlap_ratio = processor.overlap_ratio;
        processing_params.target_fs = processor.target_fs;
        processing_params.original_fs_list = [12000, 48000];
        save(fullfile(output_dir, 'processing_params.mat'), 'processing_params');
        
        % 按故障类型分别保存
        fault_types = {'Normal', 'OR', 'IR', 'B'};
        for i = 1:length(fault_types)
            fault_type = fault_types{i};
            fault_data = {};
            
            for j = 1:length(features_data)
                if strcmp(features_data{j}.fault_type, fault_type)
                    fault_data{end+1} = features_data{j};
                end
            end
            
            if ~isempty(fault_data)
                fault_table = convert_features_to_table(fault_data);
                writetable(fault_table, fullfile(output_dir, sprintf('features_%s.csv', fault_type)));
            end
        end
        
        fprintf('数据保存完成，输出目录: %s\n', output_dir);
        fprintf('  - 特征数据: extracted_features.csv/mat\n');
        fprintf('  - 处理参数: processing_params.mat\n');
        fprintf('  - 分类数据: features_Normal.csv, features_OR.csv, features_IR.csv, features_B.csv\n');
        
        % 打印数据集摘要
        print_dataset_summary(features_data);
        
    catch ME
        fprintf('保存数据失败: %s\n', ME.message);
    end
end

function feature_table = convert_features_to_table(features_data)
    % 将特征结构体数组转换为表格
    if isempty(features_data)
        feature_table = table();
        return;
    end
    
    % 获取所有字段名
    all_fields = fieldnames(features_data{1});
    n_samples = length(features_data);
    n_fields = length(all_fields);
    
    % 预分配cell数组
    data_cell = cell(n_samples, n_fields);
    
    for i = 1:n_samples
        features = features_data{i};
        for j = 1:n_fields
            field_name = all_fields{j};
            if isfield(features, field_name)
                value = features.(field_name);
                if isnumeric(value) && isscalar(value)
                    data_cell{i, j} = value;
                elseif ischar(value) || isstring(value)
                    data_cell{i, j} = char(value);
                else
                    data_cell{i, j} = '';
                end
            else
                data_cell{i, j} = NaN;
            end
        end
    end
    
    % 创建表格
    feature_table = cell2table(data_cell, 'VariableNames', all_fields);
end

function print_dataset_summary(features_data)
    % 打印数据集摘要
    if isempty(features_data)
        return;
    end
    
    fprintf('\n数据集摘要:\n');
    fprintf('  总样本数: %d\n', length(features_data));
    
    % 特征维度计算（排除元数据字段）
    all_fields = fieldnames(features_data{1});
    exclude_fields = {'file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs'};
    feature_fields = setdiff(all_fields, exclude_fields);
    fprintf('  特征维度: %d\n', length(feature_fields));
    
    % 统计各类样本数
    fprintf('  各类样本数:\n');
    fault_counts = containers.Map();
    fs_counts = containers.Map();
    
    for i = 1:length(features_data)
        features = features_data{i};
        
        % 故障类型统计
        fault_type = features.fault_type;
        if fault_counts.isKey(fault_type)
            fault_counts(fault_type) = fault_counts(fault_type) + 1;
        else
            fault_counts(fault_type) = 1;
        end
        
        % 采样率统计
        fs_key = sprintf('%d', features.original_fs);
        if fs_counts.isKey(fs_key)
            fs_counts(fs_key) = fs_counts(fs_key) + 1;
        else
            fs_counts(fs_key) = 1;
        end
    end
    
    fault_types = {'Normal', 'OR', 'IR', 'B'};
    for i = 1:length(fault_types)
        fault_type = fault_types{i};
        if fault_counts.isKey(fault_type)
            count = fault_counts(fault_type);
            percentage = count / length(features_data) * 100;
            fprintf('    %s: %d (%.1f%%)\n', fault_type, count, percentage);
        end
    end
    
    % 混合采样率统计
    fprintf('\n  原始采样率分布:\n');
    fs_keys = keys(fs_counts);
    for i = 1:length(fs_keys)
        fs_key = fs_keys{i};
        count = fs_counts(fs_key);
        percentage = count / length(features_data) * 100;
        fprintf('    %s Hz: %d (%.1f%%)\n', fs_key, count, percentage);
    end
end