import scipy.io as sio
import numpy as np
import os
from datetime import datetime


def read_mat_file_info(file_path):
    """
    读取MAT文件的基础信息和变量信息
    """
    try:
        # 读取MAT文件
        mat_data = sio.loadmat(file_path)

        # 获取文件基本信息
        file_info = os.stat(file_path)
        file_size = file_info.st_size

        print("=" * 50)
        print("MAT文件分析报告")
        print("=" * 50)

        # 文件基本信息
        print(f"📁 文件名称: {os.path.basename(file_path)}")
        print(f"📊 文件大小: {file_size / 1024:.2f} KB")
        print(f"🕐 最后修改时间: {datetime.fromtimestamp(file_info.st_mtime)}")
        print("-" * 50)

        # 显示MAT文件中的变量
        print("📋 包含的变量:")
        print("-" * 30)

        variables_info = []

        for key, value in mat_data.items():
            # 过滤掉MATLAB自动添加的系统变量（通常以__开头）
            if not key.startswith('__'):
                var_type = type(value).__name__
                var_shape = "未知形状"
                var_dtype = "未知类型"
                var_size = "未知大小"

                if hasattr(value, 'shape'):
                    var_shape = str(value.shape)
                if hasattr(value, 'dtype'):
                    var_dtype = str(value.dtype)
                if hasattr(value, 'nbytes'):
                    var_size = f"{value.nbytes / 1024:.2f} KB"

                variables_info.append({
                    'name': key,
                    'type': var_type,
                    'shape': var_shape,
                    'dtype': var_dtype,
                    'size': var_size
                })

                print(f"  🔹 {key}: {var_type} {var_shape} ({var_dtype})")
                print(f"     大小: {var_size}")

        print("-" * 50)

        # 显示系统变量信息（MATLAB自动添加的）
        print("🔧 系统变量:")
        print("-" * 30)
        for key in mat_data.keys():
            if key.startswith('__'):
                value = mat_data[key]
                print(f"  🔸 {key}: {type(value).__name__}")

        print("=" * 50)

        return mat_data, variables_info

    except Exception as e:
        print(f"❌ 读取文件时出错: {e}")
        return None, None


def display_variable_details(mat_data, variable_name):
    """
    显示特定变量的详细信息
    """
    if variable_name in mat_data:
        data = mat_data[variable_name]
        print(f"\n📊 变量 '{variable_name}' 的详细信息:")
        print("-" * 40)
        print(f"类型: {type(data)}")

        if hasattr(data, 'shape'):
            print(f"形状: {data.shape}")
        if hasattr(data, 'dtype'):
            print(f"数据类型: {data.dtype}")
        if hasattr(data, 'nbytes'):
            print(f"占用内存: {data.nbytes / 1024:.2f} KB")

        # 显示数据的前几个元素（如果是数组）
        if isinstance(data, np.ndarray):
            print(f"前几个元素: {data.flat[:5] if data.size > 5 else data.flat[:]}")

        # 如果是结构体或字典
        elif isinstance(data, np.void) or isinstance(data, dict):
            print("这是一个结构体/字典类型")

    else:
        print(f"变量 '{variable_name}' 不存在")


# 使用示例
if __name__ == "__main__":
    # 替换为你的MAT文件路径
    mat_file_path = "F:/25_09_21_huaweibei/目标域数据集/A.mat"
    # mat_file_path = "F:/25_09_21_huaweibei/源域数据集/48kHz_DE_data\OR\Orthogonal/0021/OR021@3_0.mat"

    # 读取文件信息
    mat_data, variables_info = read_mat_file_info(mat_file_path)

    if mat_data is not None:
        # 如果有变量，显示第一个非系统变量的详细信息
        if variables_info:
            first_var = variables_info[0]['name']
            display_variable_details(mat_data, first_var)

        # 显示所有可用的变量名
        print(f"\n 所有可用变量: {[key for key in mat_data.keys() if not key.startswith('__')]}")

