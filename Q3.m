function TransferLearningDiagnosisSystem()
    % 迁移学习故障诊断系统 - MATLAB版本
    % 基于源域随机森林模型的深度域适应分析
    
    % 初始化参数
    source_data_path = 'processed_data_mixed_fs';
    target_data_path = 'processed_target_data';
    target_fault_types = {'IR', 'OR', 'B'};
    
    % 主要执行流程
    fprintf('迁移学习故障诊断系统\n');
    fprintf('=====================================\n\n');
    
    try
        % 步骤1: 加载源域模型和数据
        fprintf('步骤1: 加载源域模型和数据\n');
        [source_features, source_labels, feature_names, source_rf_model, scaler_params] = ...
            load_source_model_and_data(source_data_path, target_fault_types);
        
        % 步骤2: 加载目标域数据
        fprintf('\n步骤2: 加载目标域数据\n');
        [target_features, target_df] = load_target_domain_data(target_data_path, ...
            feature_names, scaler_params);
        
        % 步骤3: 深度域间差异分析
        fprintf('\n步骤3: 深度域间差异分析\n');
        domain_gap_analysis = deep_domain_gap_analysis(source_features, target_features, ...
            feature_names, source_rf_model);
        
        % 步骤4: 多策略域适应
        fprintf('\n步骤4: 多策略域适应\n');
        domain_adapted_models = multi_strategy_domain_adaptation(source_features, ...
            source_labels, target_features, source_rf_model, domain_gap_analysis);
        
        % 步骤5: 文件级聚合和最终预测
        fprintf('\n步骤5: 文件级聚合和最终预测\n');
        final_predictions = file_level_aggregation_and_prediction(domain_adapted_models, ...
            source_features, source_labels, target_features, target_df, domain_gap_analysis);
        
        % 步骤6: 综合可视化
        fprintf('\n步骤6: 综合可视化\n');
        comprehensive_visualization(final_predictions, domain_gap_analysis, source_data_path);
        
        % 步骤7: 生成最终报告
        fprintf('\n步骤7: 生成最终报告\n');
        generate_final_report(final_predictions, target_fault_types, source_data_path);
        
        fprintf('\n迁移学习故障诊断完成！\n');
        
    catch ME
        fprintf('执行过程中出现错误: %s\n', ME.message);
        rethrow(ME);
    end
end

function [source_features, source_labels, feature_names, source_rf_model, scaler_params] = ...
    load_source_model_and_data(source_data_path, target_fault_types)
    % 加载源域模型和数据
    
    fprintf('=====================================\n');
    fprintf('加载源域模型和数据\n');
    fprintf('=====================================\n');
    
    % 1. 加载源域数据
    fprintf('1. 加载源域数据...\n');
    source_feature_file = fullfile(source_data_path, 'extracted_features.csv');
    
    if ~exist(source_feature_file, 'file')
        error('源域特征文件不存在: %s', source_feature_file);
    end
    
    % 读取CSV文件
    source_data = readtable(source_feature_file);
    fprintf('源域原始数据: %d行 x %d列\n', height(source_data), width(source_data));
    
    % 过滤故障类型
    if ismember('fault_type', source_data.Properties.VariableNames)
        fault_mask = ismember(source_data.fault_type, target_fault_types);
        source_data = source_data(fault_mask, :);
        fprintf('过滤后源域数据: %d行 x %d列\n', height(source_data), width(source_data));
    end
    
    % 提取特征列
    exclude_cols = {'file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs'};
    feature_names = source_data.Properties.VariableNames;
    feature_names = feature_names(~ismember(feature_names, exclude_cols));
    
    % 提取特征和标签
    source_features = table2array(source_data(:, feature_names));
    if ismember('fault_type', source_data.Properties.VariableNames)
        source_labels = source_data.fault_type;
    else
        source_labels = repmat({'Normal'}, height(source_data), 1);
    end
    
    % 数据清理
    source_features = clean_data(source_features);
    
    % 分位数标准化
    fprintf('执行分位数标准化...\n');
    [source_features, scaler_params] = quantile_transform(source_features);
    
    % 2. 训练源域随机森林模型
    fprintf('\n2. 训练源域随机森林模型...\n');
    source_rf_model = train_random_forest(source_features, source_labels);
    
    % 源域性能评估
    source_pred = predict(source_rf_model, source_features);
    source_acc = sum(strcmp(source_pred, source_labels)) / length(source_labels);
    fprintf('源域模型准确率: %.4f\n', source_acc);
    
    fprintf('源域模型训练完成: 特征数=%d, 样本数=%d\n', ...
        length(feature_names), size(source_features, 1));
end

function [target_features, target_df] = load_target_domain_data(target_data_path, ...
    feature_names, scaler_params)
    % 加载目标域数据
    
    fprintf('3. 加载目标域数据...\n');
    
    % 尝试加载目标域数据文件
    target_file1 = fullfile(target_data_path, 'target_features_compatible.csv');
    target_file2 = fullfile(target_data_path, 'target_features.csv');
    
    if exist(target_file1, 'file')
        target_file = target_file1;
    elseif exist(target_file2, 'file')
        target_file = target_file2;
    else
        error('未找到目标域特征文件');
    end
    
    target_df = readtable(target_file);
    fprintf('目标域数据: %d行 x %d列\n', height(target_df), width(target_df));
    
    % 特征对齐
    target_exclude_cols = {'file_path', 'fault_type', 'sensor_type', 'rpm', ...
        'original_fs', 'resampled_fs', 'source_file', 'sample_index', 'file_name'};
    available_cols = target_df.Properties.VariableNames;
    available_cols = available_cols(~ismember(available_cols, target_exclude_cols));
    
    % 补充缺失特征
    missing_features = setdiff(feature_names, available_cols);
    for i = 1:length(missing_features)
        target_df.(missing_features{i}) = zeros(height(target_df), 1);
    end
    
    % 提取特征
    target_features = table2array(target_df(:, feature_names));
    target_features = clean_data(target_features);
    
    % 应用相同的标准化
    target_features = apply_quantile_transform(target_features, scaler_params);
    
    if ismember('source_file', target_df.Properties.VariableNames)
        target_files = unique(target_df.source_file);
        fprintf('目标域文件: %d 个\n', length(target_files));
    end
    
    fprintf('目标域数据处理完成: %d行 x %d列\n', size(target_features, 1), size(target_features, 2));
end

function cleaned_data = clean_data(data)
    % 数据清理函数
    
    cleaned_data = data;
    [n_samples, n_features] = size(data);
    
    for i = 1:n_features
        col_data = data(:, i);
        
        % 处理NaN和无穷值
        finite_mask = isfinite(col_data);
        
        if ~all(finite_mask)
            if any(finite_mask)
                median_val = median(col_data(finite_mask));
            else
                median_val = 0;
            end
            cleaned_data(~finite_mask, i) = median_val;
        end
        
        % 处理异常值
        if any(finite_mask) && std(col_data(finite_mask)) > 0
            mean_val = mean(col_data(finite_mask));
            std_val = std(col_data(finite_mask));
            outlier_mask = abs(col_data - mean_val) > 3 * std_val;
            
            if any(outlier_mask)
                p95 = prctile(col_data(finite_mask), 95);
                p05 = prctile(col_data(finite_mask), 5);
                cleaned_data(outlier_mask & (col_data > mean_val), i) = p95;
                cleaned_data(outlier_mask & (col_data < mean_val), i) = p05;
            end
        end
    end
    
    % 最后检查
    cleaned_data(~isfinite(cleaned_data)) = 0;
end

function [transformed_data, scaler_params] = quantile_transform(data)
    % 分位数变换标准化
    
    [n_samples, n_features] = size(data);
    transformed_data = zeros(size(data));
    scaler_params = struct();
    
    n_quantiles = min(1000, n_samples);
    
    for i = 1:n_features
        col_data = data(:, i);
        
        % 计算分位数
        quantiles = linspace(0, 1, n_quantiles);
        quantile_values = quantile(col_data, quantiles);
        
        % 存储参数
        scaler_params.quantile_values{i} = quantile_values;
        scaler_params.quantiles{i} = quantiles;
        
        % 变换数据
        for j = 1:length(col_data)
            % 找到最近的分位数
            [~, idx] = min(abs(quantile_values - col_data(j)));
            transformed_data(j, i) = quantiles(idx);
        end
    end
end

function transformed_data = apply_quantile_transform(data, scaler_params)
    % 应用分位数变换
    
    [n_samples, n_features] = size(data);
    transformed_data = zeros(size(data));
    
    for i = 1:n_features
        col_data = data(:, i);
        quantile_values = scaler_params.quantile_values{i};
        quantiles = scaler_params.quantiles{i};
        
        % 变换数据
        for j = 1:length(col_data)
            [~, idx] = min(abs(quantile_values - col_data(j)));
            transformed_data(j, i) = quantiles(idx);
        end
    end
end

function rf_model = train_random_forest(features, labels)
    % 训练随机森林模型
    
    % MATLAB的随机森林参数
    rf_model = TreeBagger(300, features, labels, ...
        'Method', 'classification', ...
        'MinLeafSize', 2, ...
        'MaxNumSplits', 2^15-1, ...  % 相当于max_depth=15
        'NumVariablesToSample', 'all', ...
        'OOBPrediction', 'on', ...
        'OOBPredictorImportance', 'on');  % 启用OOB特征重要性
end

function domain_gap_analysis = deep_domain_gap_analysis(source_features, target_features, ...
    feature_names, source_rf_model)
    % 深度域间差异分析
    
    fprintf('=====================================\n');
    fprintf('深度域间差异分析\n');
    fprintf('=====================================\n');
    
    domain_gap_analysis = struct();
    
    % 1. MMD距离计算
    fprintf('1. 计算MMD距离...\n');
    mmd_distance = compute_mmd_distance(source_features, target_features);
    fprintf('   MMD距离: %.6f\n', mmd_distance);
    domain_gap_analysis.mmd_distance = mmd_distance;
    
    % 2. 统计差异分析
    fprintf('2. 统计差异分析...\n');
    statistical_differences = analyze_statistical_differences(source_features, ...
        target_features, feature_names);
    domain_gap_analysis.statistical_differences = statistical_differences;
    
    % 3. 分布重叠度计算
    fprintf('3. 分布重叠度分析...\n');
    overlap_analysis = analyze_distribution_overlap(source_features, target_features);
    domain_gap_analysis.overlap_analysis = overlap_analysis;
    
    % 4. 特征稳定性分析
    fprintf('4. 特征稳定性分析...\n');
    feature_stability = analyze_feature_stability(source_features, target_features, ...
        feature_names, source_rf_model);
    domain_gap_analysis.feature_stability = feature_stability;
    
    fprintf('域间差异分析完成\n');
end

function mmd_distance = compute_mmd_distance(source_data, target_data)
    % 计算MMD距离
    
    gamma = 1.0;
    n_samples = min([1000, size(source_data, 1), size(target_data, 1)]);
    
    % 随机采样
    source_idx = randperm(size(source_data, 1), n_samples);
    target_idx = randperm(size(target_data, 1), n_samples);
    
    source_sample = source_data(source_idx, :);
    target_sample = target_data(target_idx, :);
    
    % RBF核函数
    K_ss = rbf_kernel(source_sample, source_sample, gamma);
    K_tt = rbf_kernel(target_sample, target_sample, gamma);
    K_st = rbf_kernel(source_sample, target_sample, gamma);
    
    mmd_squared = mean(K_ss(:)) + mean(K_tt(:)) - 2 * mean(K_st(:));
    mmd_distance = sqrt(max(0, mmd_squared));
end

function K = rbf_kernel(X, Y, gamma)
    % RBF核函数
    
    % 计算欧氏距离矩阵
    D = pdist2(X, Y, 'euclidean').^2;
    K = exp(-gamma * D);
end

function differences = analyze_statistical_differences(source_features, target_features, feature_names)
    % 统计差异分析
    
    n_features = size(source_features, 2);
    differences = struct();
    
    for i = 1:n_features
        source_feat = source_features(:, i);
        target_feat = target_features(:, i);
        
        % KS检验
        [~, ks_pvalue, ks_stat] = kstest2(source_feat, target_feat);
        
        % Wasserstein距离（简化版本）
        wasserstein_dist = wasserstein_distance(source_feat, target_feat);
        
        % 均值和方差差异
        mean_diff = abs(mean(source_feat) - mean(target_feat));
        var_ratio = var(target_feat) / (var(source_feat) + 1e-8);
        
        differences.feature_names{i} = feature_names{i};
        differences.ks_statistic(i) = ks_stat;
        differences.ks_pvalue(i) = ks_pvalue;
        differences.wasserstein_distance(i) = wasserstein_dist;
        differences.mean_difference(i) = mean_diff;
        differences.variance_ratio(i) = var_ratio;
    end
    
    % 排序
    [~, sorted_idx] = sort(differences.ks_statistic, 'descend');
    
    fprintf('   分布差异最大的5个特征:\n');
    for i = 1:min(5, length(sorted_idx))
        idx = sorted_idx(i);
        fprintf('     %d. %s: KS=%.4f\n', i, differences.feature_names{idx}(1:min(30, end)), ...
            differences.ks_statistic(idx));
    end
end

function w_dist = wasserstein_distance(x, y)
    % 简化的Wasserstein距离计算
    
    % 排序
    x_sorted = sort(x);
    y_sorted = sort(y);
    
    % 插值到相同长度
    n = max(length(x_sorted), length(y_sorted));
    x_interp = interp1(linspace(0, 1, length(x_sorted)), x_sorted, linspace(0, 1, n));
    y_interp = interp1(linspace(0, 1, length(y_sorted)), y_sorted, linspace(0, 1, n));
    
    % 计算差异
    w_dist = mean(abs(x_interp - y_interp));
end

function overlap_analysis = analyze_distribution_overlap(source_features, target_features)
    % 分布重叠度分析
    
    n_features = size(source_features, 2);
    overlap_scores = zeros(1, n_features);
    
    for i = 1:n_features
        source_feat = source_features(:, i);
        target_feat = target_features(:, i);
        
        s_q25 = prctile(source_feat, 25);
        s_q75 = prctile(source_feat, 75);
        t_q25 = prctile(target_feat, 25);
        t_q75 = prctile(target_feat, 75);
        
        overlap_start = max(s_q25, t_q25);
        overlap_end = min(s_q75, t_q75);
        
        s_range = s_q75 - s_q25;
        t_range = t_q75 - t_q25;
        
        if overlap_end > overlap_start && s_range > 0 && t_range > 0
            overlap_length = overlap_end - overlap_start;
            overlap_scores(i) = overlap_length / min(s_range, t_range);
        else
            overlap_scores(i) = 0;
        end
    end
    
    avg_overlap = mean(overlap_scores);
    fprintf('   平均分布重叠度: %.4f\n', avg_overlap);
    
    overlap_analysis.feature_overlaps = overlap_scores;
    overlap_analysis.average_overlap = avg_overlap;
    overlap_analysis.low_overlap_indices = find(overlap_scores < 0.3);
end

function feature_stability = analyze_feature_stability(source_features, target_features, ...
    feature_names, source_rf_model)
    % 特征稳定性分析
    
    % 使用源域模型的特征重要性 - 添加完整的错误处理
    rf_importance = [];
    
    % 方法1: 尝试获取OOB特征重要性
    try
        if isprop(source_rf_model, 'OOBPermutedVarDeltaError') || isfield(source_rf_model, 'OOBPermutedVarDeltaError')
            rf_importance = source_rf_model.OOBPermutedVarDeltaError;
            if ~isempty(rf_importance)
                fprintf('   使用OOB特征重要性\n');
            end
        end
    catch ME
        fprintf('   OOB重要性获取失败: %s\n', ME.message);
    end
    
    % 方法2: 如果OOB重要性不可用，尝试使用变量重要性
    if isempty(rf_importance)
        try
            if isprop(source_rf_model, 'VariableImportance') || isfield(source_rf_model, 'VariableImportance')
                rf_importance = source_rf_model.VariableImportance;
                if ~isempty(rf_importance)
                    fprintf('   使用变量重要性\n');
                end
            end
        catch ME
            fprintf('   变量重要性获取失败: %s\n', ME.message);
        end
    end
    
    % 方法3: 重新训练一个模型计算特征重要性
    if isempty(rf_importance)
        try
            fprintf('   重新训练模型计算特征重要性...\n');
            
            % 获取源域标签（从原始模型推断或使用dummy labels）
            n_samples = size(source_features, 1);
            
            % 尝试从原始模型获取类别
            try
                class_names = source_rf_model.ClassNames;
                % 创建平衡的dummy标签
                n_classes = length(class_names);
                dummy_labels = repmat(class_names, ceil(n_samples/n_classes), 1);
                dummy_labels = dummy_labels(1:n_samples);
            catch
                % 如果无法获取类别名，使用数字标签
                dummy_labels = mod(1:n_samples, 3) + 1;  % 假设3个类别
            end
            
            % 训练临时模型
            temp_model = TreeBagger(50, source_features, dummy_labels, ...
                'Method', 'classification', ...
                'MinLeafSize', 5, ...
                'OOBPrediction', 'on', ...
                'OOBPredictorImportance', 'on');
            
            rf_importance = temp_model.OOBPermutedVarDeltaError;
            fprintf('   临时模型特征重要性计算成功\n');
            
        catch ME
            fprintf('   临时模型训练失败: %s\n', ME.message);
        end
    end
    
    % 方法4: 使用基于方差的简单重要性
    if isempty(rf_importance) || length(rf_importance) ~= size(source_features, 2)
        fprintf('   使用基于方差的特征重要性\n');
        
        % 计算每个特征的方差作为重要性指标
        feature_variances = var(source_features, [], 1);
        rf_importance = feature_variances / sum(feature_variances);
    end
    
    % 确保rf_importance是行向量且长度正确
    if size(rf_importance, 1) > 1
        rf_importance = rf_importance';
    end
    
    % 再次检查长度
    if length(rf_importance) ~= size(source_features, 2)
        fprintf('   警告：重要性维度不匹配，使用均匀分布\n');
        rf_importance = ones(1, size(source_features, 2)) / size(source_features, 2);
    end
    
    % 计算特征在目标域的变异系数
    n_features = size(target_features, 2);
    target_cv = zeros(1, n_features);
    
    for i = 1:n_features
        feat_std = std(target_features(:, i));
        feat_mean = mean(target_features(:, i));
        target_cv(i) = feat_std / (abs(feat_mean) + 1e-8);
    end
    
    % 结合重要性和稳定性的综合评分
    stability_scores = rf_importance ./ (1 + target_cv);
    
    % 选择最稳定的特征
    [~, sorted_idx] = sort(stability_scores, 'descend');
    n_stable_features = min(20, length(sorted_idx));
    stable_indices = sorted_idx(1:n_stable_features);
    
    fprintf('   选择了 %d 个最稳定的特征\n', n_stable_features);
    
    feature_stability.stability_scores = stability_scores;
    feature_stability.stable_feature_indices = stable_indices;
    feature_stability.stable_features = feature_names(stable_indices);
    feature_stability.rf_importance = rf_importance;  % 保存计算的重要性
end

function domain_adapted_models = multi_strategy_domain_adaptation(source_features, ...
    source_labels, target_features, source_rf_model, domain_gap_analysis)
    % 多策略域适应
    
    fprintf('=====================================\n');
    fprintf('多策略域适应\n');
    fprintf('=====================================\n');
    
    domain_adapted_models = struct();
    
    % 策略1: CORAL二阶统计量对齐
    fprintf('1. CORAL二阶统计量对齐...\n');
    [coral_source, coral_target] = coral_alignment(source_features, target_features);
    coral_model = train_adapted_model(coral_source, source_labels);
    domain_adapted_models.coral = coral_model;
    domain_adapted_models.coral_target = coral_target;
    
    % 策略2: 批归一化域适应
    fprintf('2. 批归一化域适应...\n');
    [bn_model, bn_target] = batch_normalization_adaptation(source_features, ...
        source_labels, target_features);
    domain_adapted_models.batch_norm = bn_model;
    domain_adapted_models.bn_target = bn_target;
    
    % 策略3: 基于特征稳定性的特征选择
    fprintf('3. 特征稳定性选择...\n');
    stable_model = feature_stability_adaptation(source_features, source_labels, ...
        domain_gap_analysis);
    domain_adapted_models.feature_stable = stable_model;
    
    % 策略4: 原始模型作为基线
    domain_adapted_models.original = source_rf_model;
    
    fprintf('域适应完成，共 %d 个模型\n', 4);
end

function [coral_source, coral_target] = coral_alignment(source_features, target_features)
    % CORAL二阶统计量对齐
    
    % 计算协方差矩阵
    source_cov = cov(source_features);
    target_cov = cov(target_features);
    
    % 添加正则化项
    reg_param = 1e-3;
    n_features = size(source_cov, 1);
    source_cov = source_cov + reg_param * eye(n_features);
    target_cov = target_cov + reg_param * eye(n_features);
    
    try
        % 使用Cholesky分解
        source_chol = chol(source_cov, 'lower');
        target_chol = chol(target_cov, 'lower');
        
        % 计算变换矩阵
        transform_matrix = target_chol / source_chol;
        
        % 应用变换
        coral_source = source_features * transform_matrix';
        coral_target = target_features * transform_matrix';
        
    catch
        fprintf('     使用SVD分解进行CORAL对齐\n');
        % 使用SVD分解
        [U_s, S_s, V_s] = svd(source_cov);
        [U_t, S_t, V_t] = svd(target_cov);
        
        sqrt_source = U_s * diag(sqrt(diag(S_s) + reg_param)) * V_s';
        sqrt_target = U_t * diag(sqrt(diag(S_t) + reg_param)) * V_t';
        
        transform_matrix = pinv(sqrt_source) * sqrt_target;
        coral_source = source_features * transform_matrix';
        coral_target = target_features * transform_matrix';
    end
    
    fprintf('     CORAL对齐完成\n');
end

function adapted_model = train_adapted_model(adapted_source, source_labels)
    % 训练域适应后的模型
    
    adapted_model = TreeBagger(200, adapted_source, source_labels, ...
        'Method', 'classification', ...
        'MinLeafSize', 5, ...
        'MaxNumSplits', 2^12-1);
    
    fprintf('     适应模型训练完成\n');
end

function [bn_model, bn_target] = batch_normalization_adaptation(source_features, ...
    source_labels, target_features)
    % 批归一化域适应
    
    source_mean = mean(source_features);
    source_std = std(source_features) + 1e-8;
    
    target_mean = mean(target_features);
    target_std = std(target_features) + 1e-8;
    
    % 标准化到目标域分布
    norm_source = (source_features - source_mean) ./ source_std;
    adapted_source = norm_source .* target_std + target_mean;
    
    % 同样变换目标域数据
    norm_target = (target_features - target_mean) ./ target_std;
    bn_target = norm_target .* source_std + source_mean;
    
    bn_model = TreeBagger(200, adapted_source, source_labels, ...
        'Method', 'classification', ...
        'MinLeafSize', 5);
    
    fprintf('     批归一化适应完成\n');
end

function stable_model = feature_stability_adaptation(source_features, source_labels, ...
    domain_gap_analysis)
    % 基于特征稳定性的适应
    
    stable_indices = domain_gap_analysis.feature_stability.stable_feature_indices;
    
    % 只使用稳定特征训练模型
    stable_source = source_features(:, stable_indices);
    
    stable_model = TreeBagger(250, stable_source, source_labels, ...
        'Method', 'classification', ...
        'MinLeafSize', 2);
    
    % 将稳定特征索引保存到结构体中，而不是直接添加到TreeBagger对象
    stable_model_struct = struct();
    stable_model_struct.model = stable_model;
    stable_model_struct.stable_indices = stable_indices;
    
    stable_model = stable_model_struct;  % 返回包含模型和索引的结构体
    
    fprintf('     特征稳定性适应完成\n');
end

function final_predictions = file_level_aggregation_and_prediction(domain_adapted_models, ...
    source_features, source_labels, target_features, target_df, domain_gap_analysis)
    % 文件级聚合和预测
    
    fprintf('=====================================\n');
    fprintf('文件级聚合和最终预测\n');
    fprintf('=====================================\n');
    
    % 1. 获取集成预测结果
    [sample_predictions, sample_confidences] = ensemble_prediction_with_soft_voting(...
        domain_adapted_models, source_features, source_labels, target_features, ...
        domain_gap_analysis);
    
    % 2. KNN优化
    [optimized_predictions, optimized_confidences] = knn_optimization_for_low_confidence(...
        sample_predictions, sample_confidences, source_features, source_labels, ...
        target_features, 0.6);
    
    % 3. 文件级聚合
    fprintf('\n执行文件级聚合...\n');
    final_predictions = struct();
    
    if ismember('source_file', target_df.Properties.VariableNames)
        target_files = unique(target_df.source_file);
        
        for i = 1:length(target_files)
            file_name = target_files{i};
            file_mask = strcmp(target_df.source_file, file_name);
            file_indices = find(file_mask);
            
            if isempty(file_indices)
                continue;
            end
            
            file_preds = optimized_predictions(file_indices);
            file_confs = optimized_confidences(file_indices);
            
            % 加权投票
            [final_pred, final_conf, vote_ratio] = weighted_voting(file_preds, file_confs);
            
            final_predictions.(matlab.lang.makeValidName(file_name)) = struct(...
                'prediction', final_pred, ...
                'confidence', final_conf, ...
                'sample_count', length(file_indices), ...
                'vote_ratio', vote_ratio, ...
                'sample_predictions', {file_preds}, ...
                'sample_confidences', file_confs);
            
            fprintf('   %s: %s (置信度: %.3f, 投票率: %.2f)\n', ...
                file_name, final_pred, final_conf, vote_ratio);
        end
    else
        % 默认分组
        fprintf('   没有文件信息，使用默认分组\n');
        n_files = 16;
        samples_per_file = floor(length(optimized_predictions) / n_files);
        
        for i = 1:n_files
            file_name = sprintf('%c.mat', 64 + i);
            start_idx = (i - 1) * samples_per_file + 1;
            if i < n_files
                end_idx = i * samples_per_file;
            else
                end_idx = length(optimized_predictions);
            end
            
            file_preds = optimized_predictions(start_idx:end_idx);
            file_confs = optimized_confidences(start_idx:end_idx);
            
            if ~isempty(file_preds)
                [final_pred, final_conf, ~] = weighted_voting(file_preds, file_confs);
                
                field_name = matlab.lang.makeValidName(file_name);
                final_predictions.(field_name) = struct(...
                    'prediction', final_pred, ...
                    'confidence', final_conf, ...
                    'sample_count', length(file_preds));
            end
        end
    end
    
    file_count = length(fieldnames(final_predictions));
    fprintf('\n文件级预测完成，共 %d 个文件\n', file_count);
end

function [sample_predictions, sample_confidences] = ensemble_prediction_with_soft_voting(...
    domain_adapted_models, source_features, source_labels, target_features, domain_gap_analysis)
    % 集成预测与软投票
    
    fprintf('执行集成预测与软投票...\n');
    
    model_predictions = struct();
    model_confidences = struct();
    
    % 收集每个模型的预测结果
    model_names = fieldnames(domain_adapted_models);
    for i = 1:length(model_names)
        model_name = model_names{i};
        
        % 跳过辅助数据字段
        if strcmp(model_name, 'coral_target') || strcmp(model_name, 'bn_target')
            continue;
        end
        
        model = domain_adapted_models.(model_name);
        fprintf('执行 %s 模型预测...\n', model_name);
        
        try
            if strcmp(model_name, 'feature_stable')
                % 特征稳定性模型：处理结构体格式
                if isstruct(model)
                    stable_indices = model.stable_indices;
                    actual_model = model.model;
                else
                    % 备用方案：从domain_gap_analysis获取
                    stable_indices = domain_gap_analysis.feature_stability.stable_feature_indices;
                    actual_model = model;
                end
                target_data = target_features(:, stable_indices);
                model_for_prediction = actual_model;
            elseif strcmp(model_name, 'coral')
                % CORAL模型使用对齐后的数据
                target_data = domain_adapted_models.coral_target;
                model_for_prediction = model;
            elseif strcmp(model_name, 'batch_norm')
                % 批归一化适应
                target_data = domain_adapted_models.bn_target;
                model_for_prediction = model;
            else
                % 原始模型
                target_data = target_features;
                model_for_prediction = model;
            end
            
            % 预测
            predictions = predict(model_for_prediction, target_data);
            [~, probabilities] = predict(model_for_prediction, target_data);
            
            confidences = max(probabilities, [], 2);
            
            model_predictions.(model_name) = predictions;
            model_confidences.(model_name) = confidences;
            
            % 统计预测分布
            [unique_preds, ~, idx] = unique(predictions);
            pred_counts = accumarray(idx, 1);
            avg_conf = mean(confidences);
            
            fprintf('   %s: 平均置信度=%.3f\n', model_name, avg_conf);
            for j = 1:length(unique_preds)
                fprintf('      %s: %d\n', unique_preds{j}, pred_counts(j));
            end
            
        catch ME
            fprintf('   %s 预测失败: %s\n', model_name, ME.message);
        end
    end
    
    % 软投票集成
    fprintf('\n执行软投票集成...\n');
    [sample_predictions, sample_confidences] = soft_voting_ensemble(model_predictions, model_confidences);
end

function [ensemble_predictions, ensemble_confidences] = soft_voting_ensemble(model_predictions, model_confidences)
    % 软投票集成
    
    model_names = fieldnames(model_predictions);
    if isempty(model_names)
        ensemble_predictions = {};
        ensemble_confidences = [];
        return;
    end
    
    n_samples = length(model_predictions.(model_names{1}));
    ensemble_predictions = cell(n_samples, 1);
    ensemble_confidences = zeros(n_samples, 1);
    
    % 模型权重
    model_weights = containers.Map();
    model_weights('coral') = 0.3;
    model_weights('batch_norm') = 0.25;
    model_weights('feature_stable') = 0.25;
    model_weights('original') = 0.2;
    
    for i = 1:n_samples
        sample_votes = containers.Map();
        sample_confidences = containers.Map();
        
        for j = 1:length(model_names)
            model_name = model_names{j};
            
            if i <= length(model_predictions.(model_name))
                pred = model_predictions.(model_name){i};
                conf = model_confidences.(model_name)(i);
                
                if model_weights.isKey(model_name)
                    weight = model_weights(model_name);
                else
                    weight = 0.2;
                end
                
                if sample_votes.isKey(pred)
                    sample_votes(pred) = sample_votes(pred) + weight * conf;
                    conf_list = sample_confidences(pred);
                    conf_list(end+1) = conf;
                    sample_confidences(pred) = conf_list;
                else
                    sample_votes(pred) = weight * conf;
                    sample_confidences(pred) = conf;
                end
            end
        end
        
        % 选择得票最高的预测
        if ~isempty(sample_votes.keys)
            votes = cell2mat(sample_votes.values);
            [~, max_idx] = max(votes);
            vote_keys = sample_votes.keys;
            best_prediction = vote_keys{max_idx};
            
            conf_list = sample_confidences(best_prediction);
            if iscell(conf_list)
                best_confidence = mean(cell2mat(conf_list));
            else
                best_confidence = mean(conf_list);
            end
            
            ensemble_predictions{i} = best_prediction;
            ensemble_confidences(i) = best_confidence;
        else
            ensemble_predictions{i} = 'IR';
            ensemble_confidences(i) = 0.3;
        end
    end
end

function [optimized_predictions, optimized_confidences] = knn_optimization_for_low_confidence(...
    predictions, confidences, source_features, source_labels, target_features, threshold)
    % KNN优化低置信度样本
    
    low_conf_mask = confidences < threshold;
    low_conf_indices = find(low_conf_mask);
    
    if isempty(low_conf_indices)
        fprintf('   没有低置信度样本需要优化\n');
        optimized_predictions = predictions;
        optimized_confidences = confidences;
        return;
    end
    
    % 训练KNN模型
    knn_model = fitcknn(source_features, source_labels, 'NumNeighbors', 7, 'Distance', 'euclidean');
    
    optimized_predictions = predictions;
    optimized_confidences = confidences;
    
    improved_count = 0;
    for i = 1:length(low_conf_indices)
        idx = low_conf_indices(i);
        sample = target_features(idx, :);
        
        [knn_pred, knn_scores] = predict(knn_model, sample);
        knn_conf = max(knn_scores);
        
        % 如果KNN给出更高置信度，则采用KNN结果
        if knn_conf > confidences(idx)
            optimized_predictions{idx} = knn_pred{1};
            optimized_confidences(idx) = min(knn_conf, 0.85);
            improved_count = improved_count + 1;
        end
    end
    
    fprintf('   优化了 %d 个样本的预测\n', improved_count);
end

function [final_pred, final_conf, vote_ratio] = weighted_voting(file_preds, file_confs)
    % 加权投票
    
    if isempty(file_preds)
        final_pred = 'IR';
        final_conf = 0.3;
        vote_ratio = 0;
        return;
    end
    
    % 计算加权投票
    [unique_preds, ~, idx] = unique(file_preds);
    weighted_votes = zeros(size(unique_preds));
    
    for i = 1:length(unique_preds)
        mask = idx == i;
        weighted_votes(i) = sum(file_confs(mask));
    end
    
    [~, max_idx] = max(weighted_votes);
    final_pred = unique_preds{max_idx};
    
    % 计算最终置信度
    pred_mask = strcmp(file_preds, final_pred);
    final_conf = mean(file_confs(pred_mask));
    
    % 计算投票率
    vote_ratio = sum(pred_mask) / length(file_preds);
    
    % 如果投票率很高，提升置信度
    if vote_ratio > 0.8
        final_conf = min(0.95, final_conf + 0.1);
    end
end

function comprehensive_visualization(final_predictions, domain_gap_analysis, source_data_path)
    % 综合可视化
    
    fprintf('=====================================\n');
    fprintf('综合可视化\n');
    fprintf('=====================================\n');
    
    if isempty(fieldnames(final_predictions))
        fprintf('没有预测结果进行可视化\n');
        return;
    end
    
    % 提取数据
    field_names = fieldnames(final_predictions);
    file_names = cell(size(field_names));
    predictions = cell(size(field_names));
    confidences = zeros(size(field_names));
    sample_counts = zeros(size(field_names));
    
    for i = 1:length(field_names)
        result = final_predictions.(field_names{i});
        file_names{i} = field_names{i};
        predictions{i} = result.prediction;
        confidences(i) = result.confidence;
        sample_counts(i) = result.sample_count;
    end
    
    % 创建图形窗口
    figure('Position', [100, 100, 1200, 900], 'Color', [0.95, 0.95, 0.95]);
    
    % 1. 故障类型分布饼图
    subplot(2, 3, 1);
    [unique_preds, ~, idx] = unique(predictions);
    pred_counts = accumarray(idx, 1);
    colors = [1, 0.4, 0.6; 0.3, 0.8, 0.8; 0.3, 0.7, 0.8];
    pie(pred_counts, unique_preds);
    colormap(colors(1:length(unique_preds), :));
    title('故障类型分布', 'FontSize', 12, 'FontWeight', 'bold');
    
    % 2. 置信度分布直方图
    subplot(2, 3, 2);
    histogram(confidences, 12, 'FaceColor', [0.5, 0.7, 1], 'EdgeColor', 'white');
    hold on;
    mean_conf = mean(confidences);
    line([mean_conf, mean_conf], ylim, 'Color', 'r', 'LineStyle', '--', 'LineWidth', 2);
    xlabel('置信度分数');
    ylabel('频数');
    title('置信度分布', 'FontSize', 12, 'FontWeight', 'bold');
    legend({sprintf('置信度分布'), sprintf('均值: %.3f', mean_conf)}, 'Location', 'best');
    grid on; grid minor;
    
    % 3. 置信度 vs 样本数散点图
    subplot(2, 3, 3);
    color_map = containers.Map({'IR', 'OR', 'B'}, {[1, 0.4, 0.6], [0.3, 0.8, 0.8], [0.3, 0.7, 0.8]});
    hold on;
    for i = 1:length(unique_preds)
        pred_type = unique_preds{i};
        mask = strcmp(predictions, pred_type);
        if color_map.isKey(pred_type)
            color = color_map(pred_type);
        else
            color = [0.5, 0.5, 0.5];
        end
        scatter(sample_counts(mask), confidences(mask), 60, color, 'filled', 'DisplayName', pred_type);
    end
    
    % 添加趋势线
    if length(sample_counts) > 1
        p = polyfit(sample_counts, confidences, 1);
        x_trend = linspace(min(sample_counts), max(sample_counts), 100);
        y_trend = polyval(p, x_trend);
        plot(x_trend, y_trend, 'r--', 'LineWidth', 1.5, 'DisplayName', '趋势线');
    end
    
    xlabel('每文件样本数');
    ylabel('预测置信度');
    title('置信度 vs 样本数关系', 'FontSize', 12, 'FontWeight', 'bold');
    legend('show');
    grid on; grid minor;
    
    % 4. 文件级置信度柱状图
    subplot(2, 3, 4);
    bar_colors = zeros(length(predictions), 3);
    for i = 1:length(predictions)
        if color_map.isKey(predictions{i})
            bar_colors(i, :) = color_map(predictions{i});
        else
            bar_colors(i, :) = [0.5, 0.5, 0.5];
        end
    end
    
    bar(1:length(confidences), confidences, 'FaceColor', 'flat', 'CData', bar_colors);
    hold on;
    line([1, length(confidences)], [0.8, 0.8], 'Color', [1, 0.6, 0], 'LineStyle', ':', 'LineWidth', 2);
    line([1, length(confidences)], [0.6, 0.6], 'Color', [1, 0.8, 0], 'LineStyle', ':', 'LineWidth', 2);
    
    xlabel('文件索引');
    ylabel('置信度分数');
    title('文件级预测置信度', 'FontSize', 12, 'FontWeight', 'bold');
    legend({'置信度', '高置信度 (0.8)', '中等置信度 (0.6)'}, 'Location', 'best');
    grid on; grid minor;
    
    % 5. 域适应指标
    subplot(2, 3, 5);
    if ~isempty(fieldnames(domain_gap_analysis))
        mmd_dist = domain_gap_analysis.mmd_distance;
        avg_overlap = domain_gap_analysis.overlap_analysis.average_overlap;
        
        gap_metrics = [min(1.0, mmd_dist * 100); avg_overlap; mean(confidences); ...
            1 - (length(unique_preds) - 1) / 3];
        metric_names = {'MMD距离', '分布重叠', '平均置信度', '预测一致性'};
        
        colors_gradient = [0.8, 0.2, 0.2; 0.8, 0.6, 0.2; 0.2, 0.6, 0.8; 0.2, 0.8, 0.2];
        barh(1:4, gap_metrics, 'FaceColor', 'flat', 'CData', colors_gradient);
        
        set(gca, 'YTick', 1:4, 'YTickLabel', metric_names);
        xlabel('指标值');
        title('域适应指标', 'FontSize', 12, 'FontWeight', 'bold');
        xlim([0, 1.1]);
        
        % 添加数值标签
        for i = 1:4
            text(gap_metrics(i) + 0.02, i, sprintf('%.3f', gap_metrics(i)), ...
                'VerticalAlignment', 'middle', 'FontWeight', 'bold');
        end
        
        line([0.5, 0.5], [0.5, 4.5], 'Color', [0.6, 0.6, 0.6], 'LineStyle', '--');
        line([0.8, 0.8], [0.5, 4.5], 'Color', [0.2, 0.8, 0.2], 'LineStyle', '--');
        grid on;
    else
        text(0.5, 0.5, {'域间差异分析', '数据不可用'}, 'HorizontalAlignment', 'center', ...
            'VerticalAlignment', 'middle', 'FontSize', 11, 'Color', [0.5, 0.5, 0.5], ...
            'FontStyle', 'italic');
        title('域适应指标', 'FontSize', 12, 'FontWeight', 'bold');
    end
    
    % 6. 统计摘要
    subplot(2, 3, 6);
    axis off;
    
    stats_text = {
        sprintf('总文件数: %d', length(final_predictions)),
        sprintf('唯一故障类型: %d', length(unique_preds)),
        sprintf('最高置信度: %.3f', max(confidences)),
        sprintf('最低置信度: %.3f', min(confidences)),
        sprintf('置信度标准差: %.3f', std(confidences)),
        sprintf('高置信度文件 (>0.8): %d', sum(confidences > 0.8)),
        '',
        '故障分布:'
    };
    
    for i = 1:length(unique_preds)
        stats_text{end+1} = sprintf('%s: %d (%.1f%%)', unique_preds{i}, ...
            pred_counts(i), pred_counts(i) / length(predictions) * 100);
    end
    
    text(0.05, 0.95, stats_text, 'VerticalAlignment', 'top', 'FontSize', 10, ...
        'Units', 'normalized', 'FontWeight', 'bold');
    title('统计摘要', 'FontSize', 12, 'FontWeight', 'bold');
    
    % 主标题
    sgtitle('迁移学习故障诊断 - 综合分析面板', 'FontSize', 16, 'FontWeight', 'bold');
    
    % 保存图像
    saveas(gcf, fullfile(source_data_path, 'transfer_learning_dashboard.png'));
    saveas(gcf, fullfile(source_data_path, 'transfer_learning_dashboard.fig'));
    
    fprintf('可视化完成，图像已保存\n');
end

function generate_final_report(final_predictions, target_fault_types, source_data_path)
    % 生成最终诊断报告
    
    fprintf('=====================================\n');
    fprintf('迁移学习故障诊断最终报告\n');
    fprintf('=====================================\n');
    
    if isempty(fieldnames(final_predictions))
        fprintf('没有预测结果生成报告\n');
        return;
    end
    
    % 提取数据
    field_names = fieldnames(final_predictions);
    predictions = cell(size(field_names));
    confidences = zeros(size(field_names));
    
    for i = 1:length(field_names)
        result = final_predictions.(field_names{i});
        predictions{i} = result.prediction;
        confidences(i) = result.confidence;
    end
    
    % 统计分析
    [unique_preds, ~, idx] = unique(predictions);
    pred_counts = accumarray(idx, 1);
    max_confidence = max(confidences);
    avg_confidence = mean(confidences);
    
    fprintf('\n=== 诊断结果总览 ===\n');
    fprintf('分析文件总数: %d\n', length(final_predictions));
    fprintf('最高置信度: %.4f\n', max_confidence);
    fprintf('平均置信度: %.4f\n', avg_confidence);
    
    fprintf('\n=== 故障类型分布 ===\n');
    for i = 1:length(target_fault_types)
        fault = target_fault_types{i};
        fault_idx = strcmp(unique_preds, fault);
        if any(fault_idx)
            count = pred_counts(fault_idx);
        else
            count = 0;
        end
        ratio = count / length(predictions) * 100;
        fprintf('%s: %2d 个文件 (%5.1f%%)\n', fault, count, ratio);
    end
    
    % 详细结果
    fprintf('\n=== 各文件详细诊断结果 ===\n');
    fprintf('%s\n', repmat('-', 1, 70));
    fprintf('%-8s %-8s %-10s %-8s\n', '文件', '故障类型', '置信度', '样本数');
    fprintf('%s\n', repmat('-', 1, 70));
    
    % 按置信度排序
    [~, sort_idx] = sort(confidences, 'descend');
    
    for i = 1:length(sort_idx)
        idx = sort_idx(i);
        result = final_predictions.(field_names{idx});
        file_id = strrep(field_names{idx}, '_mat', '');
        pred = result.prediction;
        conf = result.confidence;
        sample_count = result.sample_count;
        fprintf('%-8s %-8s %-10.4f %-8d\n', file_id, pred, conf, sample_count);
    end
    
    % 保存结果到CSV
    results_data = table();
    for i = 1:length(field_names)
        result = final_predictions.(field_names{i});
        results_data.File_Name{i} = field_names{i};
        results_data.Bearing_ID{i} = strrep(field_names{i}, '_mat', '');
        results_data.Fault_Type{i} = result.prediction;
        results_data.Confidence(i) = result.confidence;
        results_data.Sample_Count(i) = result.sample_count;
        if isfield(result, 'vote_ratio')
            results_data.Vote_Ratio(i) = result.vote_ratio;
        else
            results_data.Vote_Ratio(i) = 1.0;
        end
    end
    
    csv_file = fullfile(source_data_path, 'transfer_learning_diagnosis_results.csv');
    writetable(results_data, csv_file);
    
    fprintf('\n=== 域适应效果评估 ===\n');
    high_conf_count = sum(confidences > 0.8);
    fprintf('高置信度文件数 (>0.8): %d (%.1f%%)\n', high_conf_count, ...
        high_conf_count / length(confidences) * 100);
    
    fprintf('\n结果已保存到: %s\n', csv_file);
    
    fprintf('\n=== 关键结果摘要 ===\n');
    fprintf('最高置信度: %.4f\n', max_confidence);
    fprintf('平均置信度: %.4f\n', avg_confidence);
    fprintf('故障分布: ');
    for i = 1:length(unique_preds)
        fprintf('%s:%d ', unique_preds{i}, pred_counts(i));
    end
    fprintf('\n');
end